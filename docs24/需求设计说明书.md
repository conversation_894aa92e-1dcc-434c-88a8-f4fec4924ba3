# 超级智能社(SuperAI)重构版需求设计说明书

## 1. 项目概述

### 1.1 项目背景
基于现有超级智能社项目进行全面重构，保持核心业务需求不变的前提下，采用更规范的架构设计和编码实现，提升系统的可维护性、扩展性和性能。

### 1.2 重构目标
- **架构优化**: 采用标准Java开发规范，提升代码复用性和方法共用性
- **多元化支持**: 支持多币种、多语种、多时区、多种登录方式
- **全球化部署**: 全球一套服务，不同时区/地区/语种可自由访问使用
- **缓存优化**: 建立Redis和本地缓存机制，优化数据查询和处理效率
- **配置管理**: 数据库配置化管理，支持动态开关和排序控制

### 1.3 技术架构
- **重构包路径**: com.hncboy.chatgpt (保持现有包结构)
- **基础框架**: Spring Boot 2.7.x (保持现有版本)
- **数据库**: MySQL 8.0 + MyBatis-Plus 3.5.x
- **缓存**: Redis 6.x + Caffeine本地缓存
- **权限认证**: Sa-Token 1.34.x (替换现有认证方案)
- **第三方登录**: JustAuth 1.16.x (新增)
- **支付集成**: pay-java-parent 2.13.x (替换现有支付方案)
- **国际化**: Spring MessageSource + 多时区支持 (新增)
- **AI模型通道**: 基于现有channel_config/site_info/model三表优化

## 2. 核心功能模块

### 2.1 用户功能模块

#### 2.1.1 用户信息管理
**功能描述**: 统一的用户信息存储和管理

**核心特性**:
- 主键ID作为唯一用户标识
- 支持多维度用户信息存储
- 用户状态管理(正常/禁用/注销)
- 注销后历史信息保留机制

**业务规则**:
- 注销用户再次登录时能找到历史信息
- 区分首次使用和历史用户，控制新用户赠品逻辑
- 多业务场景下的注销限制机制

#### 2.1.2 多渠道登录认证
**功能描述**: 基于JustAuth组件实现多种登录方式

**支持的登录方式**:
1. **微信开放平台**: 服务号 + 订阅号
2. **账号密码**: 传统用户名密码登录
3. **手机号验证码**: 国内/国际手机号 + 短信验证码
4. **邮箱验证码**: 邮箱 + 邮件验证码
5. **Google账号**: Google OAuth2登录
6. **Facebook账号**: Facebook OAuth2登录
7. **浏览器指纹**: 设备指纹识别登录

**技术实现**:
- 统一的联合登录表设计(user_joint_login)
- 第三方授权配置表(user_joint_config)
- 数据库配置化的登录方式开关和排序
- 注册渠道标识(zns/tarot等)

#### 2.1.3 用户注销功能
**功能描述**: 安全的用户注销机制

**业务逻辑**:
- 注销状态标记，非物理删除
- 历史信息保留，支持数据恢复
- 跨业务场景的注销限制检查
- 注销后的数据脱敏处理

### 2.2 支付功能模块

#### 2.2.1 多渠道支付集成
**功能描述**: 基于pay-java-parent组件实现统一支付接入

**支持的支付方式**:
1. **支付宝**: 扫码支付、手机网站支付
2. **微信支付**: 多商户支持，按业务场景区分
3. **越南Momo支付**: v2/v3版本支持
4. **越南SE支付**: 银行转账支付
5. **扩展支持**: 预留其他支付方式接入

**技术实现**:
- 统一的支付订单表(pay_order)
- 支付渠道配置表(pay_channel_config)
- 数据库配置化的支付方式开关和排序
- 支付回调统一处理机制

#### 2.2.2 订单管理
**功能描述**: 统一的订单生命周期管理

**核心功能**:
- 订单创建和状态跟踪
- 支付结果处理和通知
- 订单超时和取消机制
- 退款和售后处理

## 3. 系统架构设计

### 3.1 包结构设计 (基于现有com.hncboy.chatgpt重构)
```
com.hncboy.chatgpt
├── common                          # 公共模块 (保持现有)
│   ├── cache                       # 缓存管理 (新增多级缓存)
│   ├── config                      # 配置管理 (增强)
│   ├── constant                    # 常量定义 (保持)
│   ├── enums                       # 枚举类 (增强)
│   ├── exception                   # 异常处理 (保持)
│   ├── i18n                        # 国际化 (新增)
│   ├── util                        # 工具类 (保持)
│   └── validation                  # 参数校验 (增强)
├── framework                       # 框架核心 (重构)
│   ├── auth                        # 认证授权 (重构为JustAuth+Sa-Token)
│   ├── cache                       # 缓存框架 (新增Redis+Caffeine)
│   ├── config                      # 框架配置 (增强)
│   ├── interceptor                 # 拦截器 (保持)
│   ├── security                    # 安全框架 (重构)
│   ├── payment                     # 支付框架 (新增pay-java-parent)
│   └── web                         # Web框架 (保持)
├── admin                           # 管理后台 (保持现有)
├── api                             # API接口 (保持现有结构，重构内容)
│   ├── controller                  # 控制器层
│   │   ├── UserController.java    # 用户管理 (重构)
│   │   ├── AuthController.java    # 认证控制器 (重构)
│   │   ├── ChatController.java    # 聊天控制器 (保持)
│   │   ├── PaymentController.java # 支付控制器 (重构)
│   │   ├── TarotController.java   # 塔罗牌控制器 (保持)
│   │   └── SystemController.java  # 系统管理 (保持)
│   ├── service                     # 服务层
│   │   ├── user                    # 用户服务 (重构)
│   │   │   ├── UserService.java
│   │   │   ├── UserAuthService.java
│   │   │   └── UserJointLoginService.java
│   │   ├── payment                 # 支付服务 (重构)
│   │   │   ├── PaymentService.java
│   │   │   ├── PaymentChannelService.java
│   │   │   └── channel             # 支付渠道实现
│   │   │       ├── AlipayChannel.java
│   │   │       ├── WechatPayChannel.java
│   │   │       ├── MomoPayChannel.java
│   │   │       └── SePayChannel.java
│   │   ├── chat                    # 聊天服务 (保持现有，增强通道选择)
│   │   │   ├── ChatService.java
│   │   │   ├── ChannelSelectorService.java  # 新增
│   │   │   └── ChannelFailoverService.java  # 新增
│   │   ├── tarot                   # 塔罗牌服务 (保持)
│   │   └── system                  # 系统服务 (保持)
│   └── mapper                      # 数据访问层 (保持现有结构)
├── base                            # 基础模块 (保持现有)
└── ChatgptApplication.java        # 主启动类 (保持)
```

### 3.2 缓存架构设计

#### 3.2.1 多级缓存策略
- **L1缓存**: Caffeine本地缓存，存储热点数据
- **L2缓存**: Redis分布式缓存，存储共享数据
- **缓存穿透**: 布隆过滤器防护
- **缓存雪崩**: 随机过期时间 + 分布式锁

#### 3.2.2 缓存管理接口
```java
// 缓存管理服务
public interface CacheManagerService {
    // 清空指定缓存
    void clearCache(String cacheName);
    // 刷新指定缓存
    void refreshCache(String cacheName, String key);
    // 批量清空缓存
    void clearCaches(List<String> cacheNames);
    // 获取缓存统计信息
    CacheStats getCacheStats(String cacheName);
}
```

### 3.3 国际化支持

#### 3.3.1 多语种支持
- **中文**: zh_CN
- **英文**: en_US
- **越南语**: vi_VN
- **扩展语言**: 支持动态添加

#### 3.3.2 多币种支持
- **人民币**: CNY
- **美元**: USD
- **越南盾**: VND
- **扩展币种**: 支持动态配置

#### 3.3.3 多时区支持
- **亚洲/上海**: Asia/Shanghai (UTC+8)
- **亚洲/胡志明**: Asia/Ho_Chi_Minh (UTC+7)
- **美国/纽约**: America/New_York (UTC-5/-4)
- **欧洲/伦敦**: Europe/London (UTC+0/+1)

## 4. 数据库设计原则

### 4.1 表设计规范
- **命名规范**: 小写字母 + 下划线
- **主键设计**: 统一使用id作为主键，bigint类型
- **时间字段**: 统一使用datetime类型，支持时区转换
- **状态字段**: 统一使用tinyint类型，枚举值标准化
- **软删除**: 使用deleted字段标记删除状态

### 4.2 索引设计规范
- **主键索引**: 每张表必须有主键
- **唯一索引**: 业务唯一字段建立唯一索引
- **普通索引**: 查询频繁字段建立索引
- **复合索引**: 多字段联合查询建立复合索引

### 4.3 字段设计规范
- **字符集**: 统一使用utf8mb4
- **排序规则**: utf8mb4_unicode_ci
- **注释**: 每个表和字段必须有中文注释
- **默认值**: 合理设置字段默认值

## 5. 接口设计规范

### 5.1 RESTful API设计
- **URL规范**: /api/v1/{module}/{resource}
- **HTTP方法**: GET(查询)、POST(创建)、PUT(更新)、DELETE(删除)
- **状态码**: 标准HTTP状态码 + 业务状态码
- **响应格式**: 统一的JSON响应格式

### 5.2 接口版本管理
- **版本策略**: URL路径版本控制
- **向后兼容**: 保持API向后兼容性
- **废弃策略**: 渐进式API废弃和迁移

### 5.3 接口安全设计
- **认证**: Sa-Token JWT认证
- **授权**: 基于角色的权限控制
- **限流**: 接口访问频率限制
- **签名**: 重要接口数字签名验证

## 6. 性能优化策略

### 6.1 数据库优化
- **连接池**: HikariCP连接池优化
- **读写分离**: 主从数据库分离
- **分库分表**: 大表水平拆分
- **索引优化**: 定期分析和优化索引

### 6.2 缓存优化
- **缓存预热**: 系统启动时预加载热点数据
- **缓存更新**: 数据变更时及时更新缓存
- **缓存监控**: 缓存命中率和性能监控

### 6.3 代码优化
- **异步处理**: 耗时操作异步化
- **批量操作**: 减少数据库交互次数
- **对象池**: 重用昂贵对象
- **JVM调优**: 内存和垃圾回收优化

## 7. 监控和运维

### 7.1 应用监控
- **性能监控**: 接口响应时间、吞吐量
- **错误监控**: 异常统计和告警
- **业务监控**: 关键业务指标监控

### 7.2 日志管理
- **日志级别**: DEBUG、INFO、WARN、ERROR
- **日志格式**: 结构化JSON日志
- **日志收集**: ELK日志收集和分析

### 7.3 健康检查
- **应用健康**: Spring Boot Actuator
- **数据库健康**: 连接池状态检查
- **缓存健康**: Redis连接状态检查

## 8. AI模型通道选择机制

### 8.1 核心表结构关系
- **channel_config**: 通道配置表，管理模型与站点的映射关系
- **site_info**: 站点信息表，存储API站点的配置信息
- **model**: 模型表，定义可用的AI模型列表

### 8.2 通道选择算法
1. **根据模型GID查询可用通道**: 通过channel_config表关联site_info表
2. **状态过滤**: 只选择启用状态的站点和通道
3. **优先级排序**: 按照priority字段升序排列，选择最高优先级通道
4. **故障转移**: 当所有通道不可用时，重置所有通道状态并选择最高优先级通道
5. **通道探测**: 定时任务检测失败通道，自动恢复可用通道

### 8.3 通道管理功能
- **动态配置**: 数据库配置化管理通道信息
- **故障监控**: 自动检测通道异常并发送钉钉告警
- **自动恢复**: 定时探测失败通道，自动恢复可用状态
- **负载均衡**: 支持多通道负载分担

## 9. 完整业务模块清单

### 9.1 核心业务模块 (8个)
1. **用户管理模块**: 用户注册、登录、资料管理、注销
2. **聊天对话模块**: AI对话、流式响应、上下文管理
3. **智能体模块**: 智能体配置、角色定义、参数管理
4. **支付模块**: 多渠道支付、订单管理、退款处理
5. **塔罗牌模块**: 牌阵管理、随机抽牌、AI解读
6. **写作应用模块**: 写作助手、模板管理、内容生成
7. **绘画模块**: AI绘画、任务管理、图片生成
8. **系统管理模块**: 配置管理、日志记录、权限控制

### 9.2 扩展功能模块 (6个)
1. **签到模块**: 每日签到、连续签到奖励
2. **积分模块**: 积分获取、消耗、记录管理
3. **VIP会员模块**: 会员等级、权益管理
4. **推广分佣模块**: 邀请码、分佣计算、收益统计
5. **国际化模块**: 多语言、多币种、多时区支持
6. **通知模块**: 系统通知、消息推送

### 9.3 技术支撑模块 (5个)
1. **缓存模块**: Redis缓存、本地缓存、缓存管理
2. **监控模块**: 性能监控、异常监控、业务监控
3. **安全模块**: 权限认证、数据加密、防护机制
4. **文件模块**: 文件上传、存储管理、访问控制
5. **定时任务模块**: 任务调度、执行监控、结果处理

## 10. 数据表完整清单 (61张表)

### 10.1 用户相关表 (8张)
1. **user_base_info** - 用户基础信息表
2. **users** - 第三方登录用户表
3. **wx_user_info** - 微信用户信息表
4. **app_sign** - APP用户签到表
5. **user_check_in_record** - 用户签到记录表
6. **user_points_log** - 用户积分日志表
7. **user_config** - 用户配置表
8. **user_merge_info** - 用户合并记录表

### 10.2 聊天相关表 (4张)
1. **chat_room** - 聊天室表
2. **chat_message** - 聊天消息表
3. **chat_agent** - 智能体配置表
4. **model** - 模型表

### 10.3 AI通道管理表 (2张)
1. **channel_config** - 通道配置表
2. **site_info** - 站点信息表

### 10.4 支付相关表 (8张)
1. **product** - 产品信息表
2. **al_orders** - 支付宝订单表
3. **wx_pay_order** - 微信支付订单表
4. **se_pay_order** - SE支付订单表
5. **qr_payment** - 二维码支付记录表
6. **transaction** - 交易表
7. **recharge_log** - 充值记录表
8. **transfer_info** - 提现申请信息表

### 10.5 塔罗牌相关表 (5张)
1. **tarot_spread** - 塔罗牌阵表
2. **tarot_card_meaning** - 塔罗牌义表
3. **tarot_reading_record** - 塔罗解读记录表
4. **tarot_daily_insight** - 塔罗每日洞察表
5. **tarot_i18n** - 塔罗国际化翻译表

### 10.6 写作应用表 (3张)
1. **write_agent** - 写作应用表
2. **write_category** - 写作分类表
3. **write_message** - 写作记录表

### 10.7 绘画相关表 (3张)
1. **draw_room** - 绘图板表
2. **draw_message** - 绘图消息表
3. **task_record** - 任务记录表

### 10.8 系统管理表 (15张)
1. **sys_config** - 系统配置表
2. **sys_oper_log** - 操作日志表
3. **sys_user** - 系统用户表
4. **sys_role** - 角色信息表
5. **sys_menu** - 菜单权限表
6. **sys_dept** - 部门表
7. **sys_post** - 岗位信息表
8. **sys_dict_type** - 字典类型表
9. **sys_dict_data** - 字典数据表
10. **sys_notice** - 通知公告表
11. **sys_job** - 定时任务表
12. **sys_job_log** - 定时任务日志表
13. **sys_logininfor** - 登录日志表
14. **sys_user_role** - 用户角色关联表
15. **sys_role_menu** - 角色菜单关联表

### 10.9 其他业务表 (13张)
1. **category_info** - 分类信息表
2. **commission_identity** - 分佣身份表
3. **exception_log** - 异常日志表
4. **home_config** - 首页配置表
5. **intelligent_fav** - 智能体收藏表
6. **promotion_info** - 促销活动信息表
7. **prompter_info** - 提示词信息表
8. **record_log** - 记录日志表
9. **role_msg_template** - 角色消息模板表
10. **sensitive_word** - 敏感词表
11. **share_info** - 分享点击信息表
12. **sys_role_dept** - 角色部门关联表
13. **sys_user_post** - 用户岗位关联表

## 11. 部署和发布

### 11.1 环境管理
- **开发环境**: dev
- **测试环境**: test
- **预生产环境**: pre
- **生产环境**: prod

### 11.2 配置管理
- **配置中心**: 数据库配置化管理
- **环境隔离**: 不同环境独立配置
- **配置热更新**: 支持配置动态刷新

### 11.3 发布策略
- **蓝绿部署**: 零停机发布
- **灰度发布**: 渐进式流量切换
- **回滚机制**: 快速回滚到上一版本
