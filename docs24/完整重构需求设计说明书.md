# 超级智能社(SuperAI)完整重构需求设计说明书

## 1. 重构概述

### 1.1 重构背景
基于现有超级智能社项目的深度分析，结合61张数据表的完整业务逻辑，在保持核心业务需求不变的前提下，对系统进行全面重构，提升架构规范性、代码可维护性和系统扩展性。

### 1.2 重构目标
- **保持业务连续性**: 100%复刻原有功能的所有业务逻辑
- **架构标准化**: 采用标准Java开发规范，提升代码复用性
- **多元化支持**: 支持多币种、多语种、多时区、多种登录方式
- **性能优化**: 建立Redis和本地缓存机制，优化数据查询效率
- **配置管理**: 数据库配置化管理，支持动态开关和排序控制
- **全球化部署**: 全球一套服务，不同地区可自由访问使用

### 1.3 重构范围
- **包路径**: 保持现有com.hncboy.chatgpt，在此基础上重构
- **数据库**: 基于现有61张表结构，优化和新增必要表
- **核心功能**: 用户管理、AI对话、支付系统、塔罗牌、绘画功能等
- **新增功能**: 多渠道登录、统一支付、AI模型通道优化、内容审核等

## 2. 技术架构重构

### 2.1 技术栈升级
```yaml
基础框架:
  - Spring Boot: 2.7.x (保持现有)
  - MyBatis-Plus: 3.5.x (保持现有)
  - MySQL: 8.0 (保持现有)

新增组件:
  - 权限认证: Sa-Token 1.34.x (替换现有认证)
  - 第三方登录: JustAuth 1.16.x (新增)
  - 支付集成: pay-java-parent 2.13.x (替换现有支付)
  - 缓存框架: Redis 6.x + Caffeine (增强现有缓存)
  - 国际化: Spring MessageSource (新增)

保持组件:
  - Web框架: Spring MVC
  - 数据库连接池: HikariCP
  - JSON处理: Jackson
  - 日志框架: Logback
```

### 2.2 包结构重构设计
```
com.hncboy.chatgpt
├── common                          # 公共模块 (保持现有，增强功能)
│   ├── cache                       # 缓存管理 (新增多级缓存支持)
│   ├── config                      # 配置管理 (增强国际化配置)
│   ├── constant                    # 常量定义 (保持现有)
│   ├── enums                       # 枚举类 (增强业务枚举)
│   ├── exception                   # 异常处理 (保持现有)
│   ├── i18n                        # 国际化 (新增)
│   ├── util                        # 工具类 (保持现有)
│   └── validation                  # 参数校验 (增强校验规则)
├── framework                       # 框架核心 (重构)
│   ├── auth                        # 认证授权 (重构为JustAuth+Sa-Token)
│   │   ├── provider                # 认证提供者
│   │   ├── handler                 # 认证处理器
│   │   └── config                  # 认证配置
│   ├── cache                       # 缓存框架 (新增Redis+Caffeine)
│   │   ├── manager                 # 缓存管理器
│   │   ├── config                  # 缓存配置
│   │   └── annotation              # 缓存注解
│   ├── payment                     # 支付框架 (新增pay-java-parent)
│   │   ├── channel                 # 支付渠道抽象
│   │   ├── config                  # 支付配置
│   │   └── callback                # 支付回调
│   ├── i18n                        # 国际化框架 (新增)
│   ├── security                    # 安全框架 (增强)
│   └── web                         # Web框架 (保持现有)
├── admin                           # 管理后台 (保持现有)
├── api                             # API接口 (保持结构，重构内容)
│   ├── controller                  # 控制器层
│   ├── service                     # 服务层
│   └── mapper                      # 数据访问层
├── base                            # 基础模块 (保持现有)
└── ChatgptApplication.java        # 主启动类 (保持)
```

## 3. 核心功能重构设计

### 3.1 用户功能重构

#### 3.1.1 用户信息管理重构
**现状分析**:
- 现有user_base_info表作为主用户表
- users表存储第三方登录信息
- wx_user_info表存储微信用户信息

**重构方案**:
```java
// 保持现有表结构，增强业务逻辑
@Service
public class UserServiceImpl implements UserService {
    
    /**
     * 统一用户信息获取 - 整合多表数据
     */
    @Override
    public UserInfoVO getUserInfo(Integer userId) {
        // 1. 获取基础用户信息
        UserBaseInfo baseInfo = userBaseInfoMapper.selectById(userId);
        
        // 2. 获取第三方登录信息
        Users thirdPartyInfo = null;
        if (baseInfo.getUsersId() != null) {
            thirdPartyInfo = usersMapper.selectById(baseInfo.getUsersId());
        }
        
        // 3. 获取微信用户信息
        WxUserInfo wxInfo = wxUserInfoMapper.selectByUserId(userId);
        
        // 4. 整合用户信息
        return buildUserInfoVO(baseInfo, thirdPartyInfo, wxInfo);
    }
}
```

#### 3.1.2 多渠道登录重构
**新增表结构**:
```sql
-- 联合登录表 (新增)
CREATE TABLE `user_joint_login` (
  `id` bigint AUTO_INCREMENT PRIMARY KEY,
  `user_id` int NOT NULL COMMENT '关联user_base_info.id',
  `login_type` varchar(20) NOT NULL COMMENT '登录类型',
  `third_party_id` varchar(100) NOT NULL COMMENT '第三方唯一标识',
  `third_party_data` text COMMENT '第三方数据(JSON)',
  `access_token` varchar(500) COMMENT '访问令牌',
  `refresh_token` varchar(500) COMMENT '刷新令牌',
  `expire_time` datetime COMMENT '令牌过期时间',
  `status` tinyint DEFAULT 1 COMMENT '状态(0:禁用 1:启用)',
  `bind_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '绑定时间',
  `last_use_time` datetime COMMENT '最后使用时间',
  UNIQUE KEY `uk_type_third_id` (`login_type`, `third_party_id`),
  KEY `idx_user_id` (`user_id`)
) COMMENT '用户联合登录表';

-- 联合登录配置表 (新增)
CREATE TABLE `user_joint_config` (
  `id` int AUTO_INCREMENT PRIMARY KEY,
  `login_type` varchar(20) NOT NULL COMMENT '登录类型',
  `config_name` varchar(100) NOT NULL COMMENT '配置名称',
  `client_id` varchar(255) NOT NULL COMMENT '客户端ID',
  `client_secret` varchar(255) NOT NULL COMMENT '客户端密钥',
  `redirect_uri` varchar(255) COMMENT '回调地址',
  `scope` varchar(255) COMMENT '授权范围',
  `config_params` text COMMENT '配置参数(JSON)',
  `enabled` tinyint DEFAULT 1 COMMENT '是否启用',
  `sort_order` int DEFAULT 0 COMMENT '排序权重',
  `environment` varchar(20) DEFAULT 'prod' COMMENT '适用环境',
  UNIQUE KEY `uk_type_env` (`login_type`, `environment`)
) COMMENT '联合登录配置表';
```

**支持的登录方式**:
1. **微信开放平台**: 服务号 + 订阅号
2. **账号密码**: 传统用户名密码登录
3. **手机号验证码**: 国内/国际手机号 + 短信验证码
4. **邮箱验证码**: 邮箱 + 邮件验证码
5. **Google账号**: Google OAuth2登录
6. **Facebook账号**: Facebook OAuth2登录
7. **浏览器指纹**: 设备指纹识别登录

#### 3.1.3 用户注销功能重构
**业务逻辑**:
```java
@Override
@Transactional(rollbackFor = Exception.class)
public Boolean deactivateUser(Integer userId, UserDeactivateDTO deactivateDTO) {
    // 1. 检查用户是否存在
    UserBaseInfo userInfo = userBaseInfoMapper.selectById(userId);
    if (userInfo == null) {
        throw new ServiceException("用户不存在");
    }
    
    // 2. 检查是否可以注销 - 跨业务场景检查
    if (!canDeactivate(userId)) {
        throw new ServiceException("用户在其他业务场景有未完成事项，无法注销");
    }
    
    // 3. 标记用户为注销状态，但保留历史数据
    userInfo.setStatus(2); // 2=注销状态
    userInfo.setUpdateTime(LocalDateTime.now());
    userBaseInfoMapper.updateById(userInfo);
    
    // 4. 处理关联数据 - 软删除或状态标记
    handleRelatedDataOnDeactivation(userId);
    
    // 5. 记录注销日志
    logUserDeactivation(userId, deactivateDTO.getReason());
    
    return true;
}

/**
 * 检查用户是否可以注销
 */
private boolean canDeactivate(Integer userId) {
    // 检查是否有未完成的订单
    if (hasUnfinishedOrders(userId)) {
        return false;
    }
    
    // 检查是否有未完成的绘画任务
    if (hasUnfinishedDrawTasks(userId)) {
        return false;
    }
    
    // 检查是否有分佣关系
    if (hasCommissionRelations(userId)) {
        return false;
    }
    
    return true;
}
```

### 3.2 支付功能重构

#### 3.2.1 统一支付架构
**基于pay-java-parent重构**:
```java
// 抽象支付渠道
public abstract class AbstractPaymentChannel {
    
    public abstract String getChannelCode();
    
    public abstract PaymentResultVO createOrder(PaymentOrder order, PaymentChannelConfig config);
    
    public abstract PaymentStatusVO queryStatus(String orderNo, PaymentChannelConfig config);
    
    public abstract Boolean handleCallback(Map<String, Object> callbackData, PaymentChannelConfig config);
    
    public abstract Boolean refund(String orderNo, BigDecimal amount, PaymentChannelConfig config);
}

// 支付宝渠道实现
@Component
public class AlipayChannel extends AbstractPaymentChannel {
    
    @Override
    public String getChannelCode() {
        return "ALIPAY";
    }
    
    @Override
    public PaymentResultVO createOrder(PaymentOrder order, PaymentChannelConfig config) {
        // 使用pay-java-parent的支付宝实现
        AliPayService aliPayService = buildAliPayService(config);
        
        // 构建支付请求
        AliPayOrderRequest request = buildOrderRequest(order);
        
        // 调用支付接口
        String payUrl = aliPayService.toPay(request);
        
        return PaymentResultVO.builder()
            .orderNo(order.getOrderNo())
            .payUrl(payUrl)
            .qrCode(generateQrCode(payUrl))
            .build();
    }
}
```

#### 3.2.2 支付渠道配置管理
**新增统一支付配置表**:
```sql
CREATE TABLE `payment_channel_config` (
  `id` bigint AUTO_INCREMENT PRIMARY KEY,
  `channel_code` varchar(20) NOT NULL COMMENT '渠道代码',
  `channel_name` varchar(100) NOT NULL COMMENT '渠道名称',
  `payment_method` varchar(20) NOT NULL COMMENT '支付方式',
  `merchant_id` varchar(100) COMMENT '商户号',
  `app_id` varchar(100) COMMENT '应用ID',
  `app_secret` varchar(255) COMMENT '应用密钥',
  `public_key` text COMMENT '公钥',
  `private_key` text COMMENT '私钥',
  `api_url` varchar(255) COMMENT 'API地址',
  `notify_url` varchar(255) COMMENT '回调地址',
  `return_url` varchar(255) COMMENT '返回地址',
  `supported_currencies` varchar(255) COMMENT '支持币种(JSON数组)',
  `config_params` text COMMENT '配置参数(JSON)',
  `enabled` tinyint DEFAULT 1 COMMENT '是否启用',
  `sort_order` int DEFAULT 0 COMMENT '排序权重',
  `environment` varchar(20) DEFAULT 'prod' COMMENT '环境',
  `fee_rate` decimal(5,4) DEFAULT 0 COMMENT '费率',
  `min_amount` decimal(12,2) DEFAULT 0 COMMENT '最小金额',
  `max_amount` decimal(12,2) DEFAULT 999999 COMMENT '最大金额',
  UNIQUE KEY `uk_channel_env` (`channel_code`, `environment`)
) COMMENT '支付渠道配置表';
```

### 3.3 AI模型通道选择优化

#### 3.3.1 基于现有三表优化
**现有表结构分析**:
- `model`: 存储AI模型定义
- `channel_config`: 存储模型与站点的映射配置
- `site_info`: 存储API站点的详细信息

**优化方案**:
```java
@Service
@Slf4j
public class OptimizedChannelSelectorService {
    
    /**
     * 智能通道选择算法 - 基于现有三表优化
     */
    @Cacheable(value = "optimal_channel", key = "#modelId + '_' + #request.hashCode()")
    public ChannelInfo selectOptimalChannel(String modelId, ChatRequest request) {
        // 1. 从model表获取模型信息
        Model model = modelMapper.selectByModelId(modelId);
        if (model == null || model.getStatus() != 1) {
            throw new ServiceException("模型不可用: " + modelId);
        }
        
        // 2. 从channel_config表获取该模型的所有通道
        List<ChannelConfig> channels = channelConfigMapper.selectByModelId(model.getId());
        
        // 3. 过滤可用通道
        channels = filterAvailableChannels(channels);
        
        // 4. 基于多因素评分选择最优通道
        ChannelConfig selectedChannel = selectByAdvancedScore(channels, request);
        
        // 5. 获取站点信息
        SiteInfo siteInfo = siteInfoMapper.selectById(selectedChannel.getSiteId());
        
        // 6. 构建通道信息
        return buildChannelInfo(model, selectedChannel, siteInfo);
    }
    
    /**
     * 高级评分算法
     */
    private ChannelConfig selectByAdvancedScore(List<ChannelConfig> channels, ChatRequest request) {
        Map<ChannelConfig, Double> scores = new HashMap<>();
        
        for (ChannelConfig channel : channels) {
            double score = calculateAdvancedScore(channel, request);
            scores.put(channel, score);
        }
        
        return scores.entrySet().stream()
            .max(Map.Entry.comparingByValue())
            .map(Map.Entry::getKey)
            .orElseThrow(() -> new ServiceException("无法选择合适的通道"));
    }
    
    /**
     * 多因素评分计算
     */
    private double calculateAdvancedScore(ChannelConfig channel, ChatRequest request) {
        double score = 0;
        
        // 1. 优先级权重 (30%)
        score += (100 - channel.getPriority()) * 0.3;
        
        // 2. 成本权重 (25%)
        score += calculateCostScore(channel) * 0.25;
        
        // 3. 性能权重 (20%)
        score += calculatePerformanceScore(channel) * 0.2;
        
        // 4. 可用性权重 (15%)
        score += calculateAvailabilityScore(channel) * 0.15;
        
        // 5. 负载权重 (10%)
        score += calculateLoadScore(channel) * 0.1;
        
        return score;
    }
}
```

## 4. 缓存架构重构

### 4.1 多级缓存设计
```java
@Configuration
@EnableCaching
public class CacheConfig {
    
    /**
     * L1缓存 - Caffeine本地缓存
     */
    @Bean
    public CacheManager caffeineCacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        cacheManager.setCaffeine(Caffeine.newBuilder()
            .maximumSize(10000)
            .expireAfterWrite(5, TimeUnit.MINUTES)
            .recordStats());
        return cacheManager;
    }
    
    /**
     * L2缓存 - Redis分布式缓存
     */
    @Bean
    public CacheManager redisCacheManager(RedisConnectionFactory factory) {
        RedisCacheConfiguration config = RedisCacheConfiguration.defaultCacheConfig()
            .entryTtl(Duration.ofMinutes(30))
            .serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer()))
            .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(new GenericJackson2JsonRedisSerializer()));
        
        return RedisCacheManager.builder(factory)
            .cacheDefaults(config)
            .build();
    }
}

/**
 * 缓存管理服务
 */
@Service
public class CacheManagerService {
    
    @Autowired
    private CacheManager caffeineCacheManager;
    
    @Autowired
    private CacheManager redisCacheManager;
    
    /**
     * 清空指定缓存
     */
    public void clearCache(String cacheName) {
        // 清空本地缓存
        Cache caffeineCache = caffeineCacheManager.getCache(cacheName);
        if (caffeineCache != null) {
            caffeineCache.clear();
        }
        
        // 清空Redis缓存
        Cache redisCache = redisCacheManager.getCache(cacheName);
        if (redisCache != null) {
            redisCache.clear();
        }
        
        log.info("已清空缓存: {}", cacheName);
    }
    
    /**
     * 刷新指定缓存
     */
    public void refreshCache(String cacheName, String key) {
        // 先清除
        evictCache(cacheName, key);
        
        // 触发重新加载 (通过调用相应的服务方法)
        triggerCacheReload(cacheName, key);
        
        log.info("已刷新缓存: {}:{}", cacheName, key);
    }
}
```

## 5. 国际化支持重构

### 5.1 多语种支持
```java
@Configuration
public class I18nConfig {
    
    @Bean
    public MessageSource messageSource() {
        ResourceBundleMessageSource messageSource = new ResourceBundleMessageSource();
        messageSource.setBasenames("i18n/messages");
        messageSource.setDefaultEncoding("UTF-8");
        messageSource.setCacheSeconds(3600);
        return messageSource;
    }
    
    @Bean
    public LocaleResolver localeResolver() {
        SessionLocaleResolver resolver = new SessionLocaleResolver();
        resolver.setDefaultLocale(Locale.SIMPLIFIED_CHINESE);
        return resolver;
    }
}

/**
 * 国际化工具类
 */
@Component
public class I18nUtil {
    
    @Autowired
    private MessageSource messageSource;
    
    public String getMessage(String key, Object... args) {
        Locale locale = LocaleContextHolder.getLocale();
        return messageSource.getMessage(key, args, key, locale);
    }
    
    public String getMessage(String key, Locale locale, Object... args) {
        return messageSource.getMessage(key, args, key, locale);
    }
}
```

### 5.2 多币种支持
```java
@Service
public class CurrencyService {
    
    private static final Map<String, String> CURRENCY_SYMBOLS = Map.of(
        "CNY", "¥",
        "USD", "$",
        "VND", "₫",
        "EUR", "€"
    );
    
    /**
     * 格式化金额显示
     */
    public String formatAmount(BigDecimal amount, String currency) {
        String symbol = CURRENCY_SYMBOLS.getOrDefault(currency, currency);
        
        // 根据币种设置小数位数
        int scale = "VND".equals(currency) ? 0 : 2;
        
        DecimalFormat formatter = new DecimalFormat();
        formatter.setMaximumFractionDigits(scale);
        formatter.setMinimumFractionDigits(scale);
        
        return symbol + formatter.format(amount);
    }
    
    /**
     * 汇率转换
     */
    public BigDecimal convertCurrency(BigDecimal amount, String fromCurrency, String toCurrency) {
        if (fromCurrency.equals(toCurrency)) {
            return amount;
        }
        
        // 获取汇率 (可以从缓存或第三方API获取)
        BigDecimal exchangeRate = getExchangeRate(fromCurrency, toCurrency);
        
        return amount.multiply(exchangeRate);
    }
}
```

## 6. 重构实施计划

### 6.1 阶段划分
```yaml
第一阶段 (基础重构):
  - 用户认证系统重构 (JustAuth + Sa-Token)
  - 缓存架构升级 (Redis + Caffeine)
  - 国际化支持添加
  - 时间: 2周

第二阶段 (支付重构):
  - 统一支付架构 (pay-java-parent)
  - 支付渠道配置管理
  - 多币种支持
  - 时间: 2周

第三阶段 (AI优化):
  - AI模型通道选择优化
  - 故障转移机制增强
  - 性能监控完善
  - 时间: 1周

第四阶段 (功能完善):
  - 内容审核系统
  - 绘画功能优化
  - 系统监控完善
  - 时间: 1周
```

### 6.2 风险控制
- **数据备份**: 重构前完整备份数据库
- **灰度发布**: 分模块逐步上线
- **回滚方案**: 每个阶段都有完整回滚方案
- **监控告警**: 实时监控系统运行状态
