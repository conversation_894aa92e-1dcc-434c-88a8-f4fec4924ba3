# 超级智能社(SuperAI)数据库表结构完整说明书

## 1. 数据库概览

### 1.1 基本信息
- **数据库名称**: super_gpt
- **表总数**: 47张表
- **字符集**: utf8mb4
- **排序规则**: utf8mb4_unicode_ci
- **行格式**: DYNAMIC
- **存储引擎**: InnoDB

### 1.2 表分类统计
| 分类 | 表数量 | 说明 |
|------|--------|------|
| 用户相关 | 7张 | 用户信息、登录、签到等 |
| 聊天相关 | 3张 | 聊天室、消息、智能体 |
| 支付相关 | 6张 | 产品、订单、多渠道支付 |
| 塔罗牌相关 | 4张 | 牌阵、牌义、解读、洞察 |
| 系统管理 | 8张 | 配置、日志、分类等 |
| 微信集成 | 5张 | 微信用户、支付、公众号 |
| 其他业务 | 14张 | 文件、通知、统计等 |

## 2. 核心业务表详细说明

### 2.1 用户相关表

#### 2.1.1 第三方用户表 (users)
**表用途**: 存储Facebook、Google等第三方登录用户信息

| 字段名 | 类型 | 默认值 | 是否为空 | 说明 | 枚举值/示例 |
|--------|------|--------|----------|------|-------------|
| id | bigint | AUTO_INCREMENT | NOT NULL | 主键 | 1, 2, 3... |
| fb_id | varchar(100) | NULL | YES | Facebook用户ID | ********** |
| google_id | varchar(100) | NULL | YES | Google用户ID | google_123456 |
| finb_id | varchar(100) | NULL | YES | 浏览器指纹值 | fp_abcd1234 |
| name | varchar(100) | NULL | YES | 用户姓名 | 张三, John Smith |
| email | varchar(100) | NULL | YES | 邮箱地址 | <EMAIL> |
| picture | varchar(255) | NULL | YES | 头像URL | https://... |
| access_token | varchar(255) | NULL | YES | Facebook访问令牌 | EAABwz... |
| last_login_time | datetime | NULL | YES | 最后登录时间 | 2024-01-01 12:00:00 |
| referrer_id | bigint | NULL | YES | 推荐人ID | 123 |
| lucky_coins | bigint | 0 | YES | 幸运币数量 | 100, 500 |
| extra_data | varchar(2000) | NULL | YES | 额外数据(JSON) | {"key":"value"} |
| created_at | datetime | CURRENT_TIMESTAMP | YES | 创建时间 | 2024-01-01 10:00:00 |
| updated_at | datetime | CURRENT_TIMESTAMP | YES | 更新时间 | 2024-01-01 11:00:00 |

**索引信息**:
- PRIMARY KEY: id
- UNIQUE KEY: fb_id, email, finb_id
- KEY: referrer_id

**业务逻辑**:
- fb_id和google_id用于第三方登录身份识别
- finb_id用于设备指纹识别，防止刷单
- lucky_coins用于塔罗牌功能的虚拟货币
- referrer_id建立推荐关系，支持推广分佣

#### 2.1.2 APP用户签到表 (app_sign)
**表用途**: 记录用户每日签到信息

| 字段名 | 类型 | 默认值 | 是否为空 | 说明 | 枚举值/示例 |
|--------|------|--------|----------|------|-------------|
| id | int unsigned | AUTO_INCREMENT | NOT NULL | 主键 | 1, 2, 3... |
| user_id | varchar(48) | NULL | YES | 用户ID | user_123 |
| interrupt_status | varchar(50) | NULL | YES | 中断状态 | normal, interrupted |
| check_date | date | NULL | YES | 签到日期 | 2024-01-01 |
| repair_check | varchar(2) | NULL | YES | 是否补签 | Y, N |
| remark | varchar(255) | NULL | YES | 备注信息 | 连续签到7天 |

**索引信息**:
- PRIMARY KEY: id
- INDEX: user_id

**业务逻辑**:
- check_date确保每日只能签到一次
- interrupt_status记录签到中断情况
- repair_check支持补签功能
- 配合积分系统发放签到奖励

#### 2.1.3 分佣身份表 (commission_identity)
**表用途**: 管理推广分佣身份和邀请码

| 字段名 | 类型 | 默认值 | 是否为空 | 说明 | 枚举值/示例 |
|--------|------|--------|----------|------|-------------|
| id | int | AUTO_INCREMENT | NOT NULL | 主键 | 1, 2, 3... |
| type | varchar(20) | NULL | YES | 分佣类型 | AGENT, PARTNER |
| code | varchar(64) | NULL | YES | 分佣编号 | COMM001 |
| status | varchar(10) | NULL | YES | 状态 | ACTIVE, INACTIVE |
| start_time | datetime | CURRENT_TIMESTAMP | NOT NULL | 生效时间 | 2024-01-01 00:00:00 |
| end_time | datetime | - | NOT NULL | 过期时间 | 2024-12-31 23:59:59 |
| percentage | int(5) | NULL | YES | 分佣比例(%) | 10, 15, 20 |
| name | varchar(100) | NULL | YES | 姓名 | 张三 |
| phone | varchar(20) | NULL | YES | 手机号 | 13800138000 |
| wx_mp_url | varchar(255) | NULL | YES | 微信公众号链接 | https://mp.weixin.qq.com/... |
| user_info_id | int | - | NOT NULL | 用户信息ID | 123 |
| open_id | varchar(64) | - | NOT NULL | 微信OpenID | oABC123... |
| invite_code | varchar(50) | - | NOT NULL | 邀请码 | INV123456 |
| create_time | datetime | CURRENT_TIMESTAMP | NOT NULL | 创建时间 | 2024-01-01 10:00:00 |
| update_time | datetime | CURRENT_TIMESTAMP | NOT NULL | 更新时间 | 2024-01-01 11:00:00 |

**业务逻辑**:
- type区分不同的分佣模式(代理商、合作伙伴等)
- percentage设置分佣比例
- invite_code用于推广链接生成
- 时间范围控制分佣身份有效期

### 2.2 聊天相关表

#### 2.2.1 聊天室表 (chat_room)
**表用途**: 存储用户聊天会话信息

| 字段名 | 类型 | 默认值 | 是否为空 | 说明 | 枚举值/示例 |
|--------|------|--------|----------|------|-------------|
| id | int unsigned | AUTO_INCREMENT | NOT NULL | 主键 | 1, 2, 3... |
| title | varchar(255) | - | NOT NULL | 房间名称 | 新对话, AI助手 |
| description | varchar(2550) | NULL | YES | 房间简介 | 这是一个AI聊天室 |
| sys_content | longtext | NULL | YES | 系统回答内容 | 你好，我是AI助手 |
| ip | varchar(64) | NULL | YES | 创建者IP | *********** |
| open | varchar(3) | NULL | YES | 是否公开 | Y, N |
| role_id | int | NULL | YES | 智能体ID | 1, 2, 3 |
| image_url | varchar(500) | NULL | YES | 房间图片 | https://... |
| open_id | varchar(64) | - | NOT NULL | 微信用户ID | oABC123... |
| conversation_id | varchar(64) | NULL | YES | 会话ID | conv_123456 |
| type | varchar(20) | CHAT | NOT NULL | 聊天室类型 | CHAT, MUSIC |
| create_time | datetime | CURRENT_TIMESTAMP | NOT NULL | 创建时间 | 2024-01-01 10:00:00 |
| update_time | datetime | CURRENT_TIMESTAMP | NOT NULL | 更新时间 | 2024-01-01 11:00:00 |
| user_id | int | NULL | YES | 用户ID | 123 |

**索引信息**:
- PRIMARY KEY: id
- INDEX: open_id

**业务逻辑**:
- type区分聊天室类型: CHAT(文字聊天), MUSIC(音乐创作)
- open控制聊天室是否公开可见
- role_id关联智能体，决定AI角色和行为
- conversation_id用于上下文管理

#### 2.2.2 聊天消息表 (chat_message)
**表用途**: 存储聊天对话消息记录

| 字段名 | 类型 | 默认值 | 是否为空 | 说明 | 枚举值/示例 |
|--------|------|--------|----------|------|-------------|
| id | bigint unsigned | AUTO_INCREMENT | NOT NULL | 主键 | 1, 2, 3... |
| parent_msg_id | bigint | NULL | YES | 父消息ID | 123 |
| user_id | int | NULL | YES | 用户ID | 456 |
| message_type | int | - | NOT NULL | 消息类型 | 1=用户消息, 2=AI回复 |
| chat_room_id | bigint | NULL | YES | 聊天室ID | 789 |
| content | text | - | NOT NULL | 消息内容 | 你好, Hello |
| model_gid | varchar(128) | NULL | YES | AI模型ID | gpt-3.5-turbo, gpt-4 |
| agent_name | varchar(64) | NULL | YES | 智能体名称 | AI助手 |
| agent_id | int | NULL | YES | 智能体ID | 1 |
| site_id | int | NULL | YES | 站点ID | 1 |
| site_name | varchar(64) | NULL | YES | 站点名称 | OpenAI |
| site_url | varchar(100) | NULL | YES | 站点URL | https://api.openai.com |
| total_tokens | bigint | NULL | YES | 消耗Token数 | 150, 300 |
| status | int(5) | 0 | YES | 消息状态 | 0=初始化, 1=完成 |
| ip | varchar(255) | NULL | YES | 用户IP | *********** |
| open_id | varchar(64) | NULL | YES | 微信OpenID | oABC123... |
| remark | longtext | NULL | YES | 备注信息 | 流式响应 |
| first_char_time | datetime | NULL | YES | 首字符时间 | 2024-01-01 10:00:01 |
| create_time | datetime | CURRENT_TIMESTAMP | NOT NULL | 创建时间 | 2024-01-01 10:00:00 |
| update_time | datetime | CURRENT_TIMESTAMP | NOT NULL | 更新时间 | 2024-01-01 10:00:02 |

**索引信息**:
- PRIMARY KEY: id
- INDEX: (open_id, chat_room_id)

**业务逻辑**:
- message_type区分用户消息(1)和AI回复(2)
- parent_msg_id构建对话上下文关系
- total_tokens用于计费和统计
- first_char_time用于性能监控
- site_*字段支持多API站点配置

#### 2.2.3 智能体表 (chat_agent)
**表用途**: 配置AI智能体角色和参数

| 字段名 | 类型 | 默认值 | 是否为空 | 说明 | 枚举值/示例 |
|--------|------|--------|----------|------|-------------|
| id | int unsigned | AUTO_INCREMENT | NOT NULL | 主键 | 1, 2, 3... |
| title | varchar(100) | NULL | YES | 智能体标题 | AI助手, 写作专家 |
| description | varchar(5000) | NULL | YES | 详细描述 | 专业的AI写作助手... |
| tag | varchar(20) | NULL | YES | 分类标签 | 写作, 翻译, 编程 |
| status | int(2) unsigned | 0 | YES | 状态 | 0=启用, 1=停用, 2=内置, 9=下架 |
| gid | varchar(128) | '' | YES | 模型ID | gpt-3.5-turbo, gpt-4 |
| model_name | varchar(64) | NULL | YES | 模型名称 | GPT-3.5, GPT-4 |
| use_cnt | int | NULL | YES | 使用次数 | 100, 500 |
| sys_content | text | NULL | YES | 系统提示词 | 你是一个专业的... |
| input_example | varchar(5000) | NULL | YES | 输入示例 | 请帮我写一篇... |
| charge | int(3) | 0 | YES | 是否收费 | 0=免费, 1=收费 |
| img_url | varchar(1000) | NULL | YES | 图标URL | https://... |
| hot | int(3) | NULL | YES | 是否热门 | 0=否, 1=是 |
| feat_recs | int(3) | NULL | YES | 是否精选 | 0=否, 1=是 |
| max_token | int(255) | NULL | YES | 最大Token数 | 2000, 4000 |
| temperature | double(11,2) | NULL | YES | 随机性参数 | 0.7, 0.9 |
| num_contexts | int | NULL | YES | 上下文数量 | 5, 10 |
| create_by | varchar(50) | '' | YES | 创建者 | admin |
| create_time | datetime | CURRENT_TIMESTAMP | YES | 创建时间 | 2024-01-01 10:00:00 |
| update_by | varchar(50) | '' | YES | 更新者 | admin |
| update_time | timestamp | CURRENT_TIMESTAMP | YES | 更新时间 | 2024-01-01 11:00:00 |
| remark | varchar(50) | '' | YES | 备注 | 测试智能体 |
| start_time | datetime | NULL | YES | 生效开始时间 | 2024-01-01 00:00:00 |
| end_time | datetime | NULL | YES | 生效结束时间 | 2024-12-31 23:59:59 |

**索引信息**:
- PRIMARY KEY: id
- INDEX: tag

**业务逻辑**:
- status控制智能体可见性和可用性
- sys_content定义AI的角色和行为模式
- temperature控制回复的创造性(0-1)
- max_token限制回复长度
- num_contexts控制对话上下文数量
- charge区分免费和付费智能体

### 2.3 支付相关表

#### 2.3.1 产品表 (product)
**表用途**: 管理可购买的产品和套餐信息

| 字段名 | 类型 | 默认值 | 是否为空 | 说明 | 枚举值/示例 |
|--------|------|--------|----------|------|-------------|
| product_id | bigint | AUTO_INCREMENT | NOT NULL | 产品ID | 1, 2, 3... |
| product_name | varchar(100) | - | NOT NULL | 产品名称 | 100积分包, VIP月卡 |
| channel | varchar(20) | NULL | YES | 充值渠道 | WECHAT, ALIPAY, SE |
| type | varchar(10) | NULL | YES | 产品类型 | CHAT, DRAW, COMMON |
| num | bigint | - | NOT NULL | 数量 | 100, 30, 365 |
| unit | varchar(10) | NULL | YES | 单位 | DAY, MONTH, YEAR, TIMES |
| remark | varchar(2000) | NULL | YES | 产品描述 | 包含100次对话... |
| button_name | varchar(10) | NULL | YES | 按钮名称 | 立即购买, 开通VIP |
| product_price | decimal(12,2) | - | NOT NULL | 产品价格 | 9.99, 29.99 |
| package_info | varchar(500) | NULL | YES | 套餐信息 | {"chat":100,"draw":10} |
| sort | int(2) | NULL | YES | 排序权重 | 1, 2, 3 |
| start_time | datetime | NULL | YES | 开始时间 | 2024-01-01 00:00:00 |
| end_time | datetime | NULL | YES | 结束时间 | 2024-12-31 23:59:59 |
| status | int | 1 | NOT NULL | 状态 | 0=启用, 1=禁用 |
| preferred_recharge | varchar(2) | '1' | YES | 推荐充值 | 0=首选, 1=非首选 |
| top_icon | varchar(100) | NULL | YES | 顶部图标 | https://... |
| create_by | varchar(50) | NULL | YES | 创建人 | admin |
| create_time | datetime | CURRENT_TIMESTAMP | NOT NULL | 创建时间 | 2024-01-01 10:00:00 |
| update_by | varchar(50) | NULL | YES | 更新人 | admin |
| update_time | datetime | CURRENT_TIMESTAMP | NOT NULL | 更新时间 | 2024-01-01 11:00:00 |
| user_reg_time_s | datetime | '0000-00-00 00:00:00' | NOT NULL | 用户注册开始时间 | 2024-01-01 00:00:00 |
| user_reg_time_e | datetime | - | NOT NULL | 用户注册结束时间 | 2024-12-31 23:59:59 |
| description | varchar(255) | NULL | YES | 详细描述 | 新用户专享优惠 |
| currency | varchar(20) | NULL | YES | 币种 | VND, USD, CNY |

**业务逻辑**:
- type区分产品类型: CHAT(对话), DRAW(绘画), COMMON(通用)
- unit定义数量单位: DAY/MONTH/YEAR(时间), TIMES(次数)
- preferred_recharge标记推荐产品
- user_reg_time_*控制新用户可见性
- package_info存储JSON格式的套餐详情

#### 2.3.2 支付宝订单表 (al_orders)
**表用途**: 管理支付宝支付订单

| 字段名 | 类型 | 默认值 | 是否为空 | 说明 | 枚举值/示例 |
|--------|------|--------|----------|------|-------------|
| orders_id | varchar(100) | - | NOT NULL | 订单ID | ALI202401010001 |
| user_id | varchar(64) | - | NOT NULL | 用户ID | user_123 |
| product_id | bigint | - | NOT NULL | 产品ID | 1 |
| product_type | varchar(10) | NULL | YES | 产品类型 | CHAT, DRAW |
| product_name | varchar(50) | - | NOT NULL | 产品名称 | 100积分包 |
| product_price | double | - | NOT NULL | 产品价格 | 9.99 |
| num | bigint | - | NOT NULL | 数量 | 100 |
| unit | varchar(10) | NULL | YES | 单位 | TIMES, DAY |
| package_info | varchar(500) | NULL | YES | 套餐信息 | {"bonus":10} |
| state | tinyint | - | NOT NULL | 订单状态 | 0=未支付, 1=成功 |
| pay_time | datetime | NULL | YES | 支付时间 | 2024-01-01 10:05:00 |
| reason_failure | varchar(50) | NULL | YES | 失败原因 | 余额不足 |
| expires_time | datetime | NULL | YES | 过期时间 | 2024-01-01 10:15:00 |
| created_time | datetime | CURRENT_TIMESTAMP | YES | 创建时间 | 2024-01-01 10:00:00 |
| update_time | datetime | CURRENT_TIMESTAMP | YES | 更新时间 | 2024-01-01 10:05:00 |

**索引信息**:
- PRIMARY KEY: orders_id
- INDEX: user_id

**业务逻辑**:
- state控制订单状态，0=待支付，1=支付成功
- expires_time设置订单过期时间，通常15分钟
- reason_failure记录支付失败原因
- 支付成功后自动发货

#### 2.3.3 SE支付订单表 (se_pay_order)
**表用途**: 管理越南SE支付(银行转账)订单

| 字段名 | 类型 | 默认值 | 是否为空 | 说明 | 枚举值/示例 |
|--------|------|--------|----------|------|-------------|
| id | bigint(11) | AUTO_INCREMENT | NOT NULL | 主键 | 1, 2, 3... |
| product_id | bigint(11) | NULL | YES | 商品ID | 1 |
| product_type | varchar(10) | NULL | YES | 商品类型 | CHAT, DRAW |
| unit | varchar(10) | NULL | YES | 单位 | TIMES, DAY |
| num | bigint | NULL | YES | 数量 | 100 |
| body | varchar(255) | NULL | YES | 商品描述 | 100积分包 |
| order_no | varchar(64) | NULL | YES | 商户订单号 | SE202401010001 |
| unique_id | varchar(100) | NULL | YES | 唯一ID | se_unique_123 |
| amount | decimal(15,2) | NULL | YES | 转账金额 | 230000.00 |
| user_id | varchar(64) | NULL | YES | 用户ID | user_123 |
| status | int(3) | 0 | YES | 订单状态 | 0=待支付, 1=成功 |
| account_number | varchar(255) | - | NOT NULL | 银行账号 | ********** |
| bank_name | varchar(255) | - | NOT NULL | 银行名称 | Vietcombank |
| qr_code_url | text | - | NOT NULL | 支付二维码URL | https://... |
| ip_address | varchar(50) | NULL | YES | IP地址 | *********** |
| gateway | varchar(100) | NULL | YES | 银行网关 | VCB_GATEWAY |
| transaction_date | datetime | NULL | YES | 交易时间 | 2024-01-01 10:05:00 |
| code | varchar(100) | NULL | YES | 付款代码 | PAY123456 |
| content | varchar(255) | NULL | YES | 转账内容 | Thanh toan don hang |
| transfer_type | varchar(50) | NULL | YES | 交易类型 | IN, OUT |
| transfer_amount | decimal(15,2) | NULL | YES | 交易金额 | 230000.00 |
| accumulated | decimal(15,2) | NULL | YES | 累计余额 | 1000000.00 |
| sub_account | varchar(100) | NULL | YES | 子账户 | SUB001 |
| reference_code | varchar(100) | NULL | YES | 参考代码 | REF123456 |
| description | text | NULL | YES | 描述 | 支付订单详情 |
| se_pay_id | varchar(64) | NULL | YES | SE支付ID | sepay_123 |
| time_end | datetime | NULL | YES | 支付完成时间 | 2024-01-01 10:10:00 |
| expires_time | datetime | NULL | YES | 过期时间 | 2024-01-01 10:15:00 |
| remark | varchar(50) | '' | YES | 备注 | 越南本地支付 |
| create_by | varchar(50) | '' | YES | 创建者 | system |
| create_time | datetime | CURRENT_TIMESTAMP | YES | 创建时间 | 2024-01-01 10:00:00 |
| update_by | varchar(50) | '' | YES | 更新者 | system |
| update_time | timestamp | CURRENT_TIMESTAMP | YES | 更新时间 | 2024-01-01 10:05:00 |

**索引信息**:
- PRIMARY KEY: id
- UNIQUE KEY: order_no

**业务逻辑**:
- SE支付是越南本地银行转账支付方式
- qr_code_url生成银行转账二维码
- transfer_type记录资金流向(IN=入账, OUT=出账)
- accumulated记录账户累计余额
- 支持多家越南银行网关

### 2.4 塔罗牌相关表

#### 2.4.1 塔罗牌阵表 (tarot_spread)
**表用途**: 配置塔罗牌阵类型和参数

| 字段名 | 类型 | 默认值 | 是否为空 | 说明 | 枚举值/示例 |
|--------|------|--------|----------|------|-------------|
| id | int(20) | AUTO_INCREMENT | NOT NULL | 主键 | 1, 2, 3... |
| name | varchar(20) | - | NOT NULL | 牌阵名称 | 三牌阵, 凯尔特十字 |
| spread_layout | varchar(32) | NULL | YES | 布局类型 | THREE, CROSS, CELTIC |
| spread_diagram_url | varchar(255) | NULL | YES | 示意图URL | https://... |
| summary | varchar(64) | NULL | YES | 简述 | 适合日常问题咨询 |
| description | varchar(255) | NULL | YES | 详细描述 | 三牌阵代表过去现在未来... |
| input_example | varchar(255) | NULL | YES | 输入示例 | 我的感情运势如何？ |
| consume | int(20) | 10 | YES | 消耗积分 | 10, 20, 50 |
| status | varchar(10) | '0' | YES | 状态 | 0=启用, 1=禁用 |
| gid | varchar(50) | 'gpt-4' | YES | AI模型 | gpt-4, gpt-3.5-turbo |
| model_key | varchar(100) | NULL | YES | 模型密钥 | key_123 |
| time_set | int(20) | NULL | YES | 告警阈值(秒) | 30, 60 |
| sort | varchar(10) | '0' | YES | 排序权重 | 1, 2, 3 |
| is_new | varchar(10) | '1' | YES | 是否新上线 | 0=新上线, 1=普通 |
| create_by | varchar(64) | NULL | YES | 创建人 | admin |
| create_time | datetime | CURRENT_TIMESTAMP | YES | 创建时间 | 2024-01-01 10:00:00 |
| update_by | varchar(64) | NULL | YES | 更新人 | admin |
| update_time | datetime | CURRENT_TIMESTAMP | YES | 更新时间 | 2024-01-01 11:00:00 |

**业务逻辑**:
- spread_layout定义牌阵布局类型
- consume设置使用该牌阵的积分消耗
- is_new标记新上线牌阵，用于推广
- time_set设置AI解读超时告警阈值

#### 2.4.2 塔罗牌义表 (tarot_card_meaning)
**表用途**: 存储78张塔罗牌的含义和图片

| 字段名 | 类型 | 默认值 | 是否为空 | 说明 | 枚举值/示例 |
|--------|------|--------|----------|------|-------------|
| id | int | AUTO_INCREMENT | NOT NULL | 主键 | 1, 2, 3... |
| name | varchar(20) | - | NOT NULL | 塔罗牌名称 | 愚者, 魔术师, 女祭司 |
| meaning | text | NULL | YES | 基本含义 | 代表新的开始和冒险... |
| tag | varchar(10) | NULL | YES | 分类标签 | 大阿卡纳, 小阿卡纳 |
| guidance_text | varchar(255) | NULL | YES | 指引语 | 勇敢迈出第一步 |
| advice | varchar(255) | NULL | YES | 正位建议 | 保持开放的心态 |
| discouraged | varchar(255) | NULL | YES | 逆位警示 | 避免鲁莽行事 |
| card_front_url | varchar(255) | NULL | YES | 正面图片URL | https://... |
| remark | varchar(255) | NULL | YES | 备注 | 大阿卡纳第0张牌 |
| create_by | varchar(64) | NULL | YES | 创建人 | admin |
| create_time | datetime | CURRENT_TIMESTAMP | YES | 创建时间 | 2024-01-01 10:00:00 |
| update_by | varchar(64) | NULL | YES | 更新人 | admin |
| update_time | datetime | CURRENT_TIMESTAMP | YES | 更新时间 | 2024-01-01 11:00:00 |
| sort | int(5) | NULL | YES | 排序 | 0, 1, 2... |

**业务逻辑**:
- name存储标准塔罗牌名称
- meaning包含牌的基本象征意义
- advice/discouraged分别对应正位和逆位的含义
- tag区分大阿卡纳(22张)和小阿卡纳(56张)

#### 2.4.3 塔罗解读记录表 (tarot_reading_record)
**表用途**: 记录用户的塔罗牌解读历史

| 字段名 | 类型 | 默认值 | 是否为空 | 说明 | 枚举值/示例 |
|--------|------|--------|----------|------|-------------|
| id | int | AUTO_INCREMENT | NOT NULL | 主键 | 1, 2, 3... |
| user_id | varchar(20) | NULL | YES | 用户ID | user_123 |
| spread_id | varchar(255) | - | NOT NULL | 牌阵ID | 1 |
| question | varchar(50) | - | NOT NULL | 用户问题 | 我的感情运势如何？ |
| answer | varchar(4000) | NULL | YES | AI解读结果 | 根据抽到的牌... |
| draw_result | varchar(2000) | - | NOT NULL | 抽牌结果(JSON) | [{"name":"愚者","position":"upright"}] |
| consume | int | - | NOT NULL | 消耗积分 | 10, 20 |
| conversation_id | varchar(64) | NULL | YES | 会话ID | conv_123 |
| status | varchar(2) | '0' | YES | 状态 | 0=未回答, 1=已回答 |
| create_by | varchar(64) | NULL | YES | 创建人 | system |
| create_time | datetime | CURRENT_TIMESTAMP | YES | 创建时间 | 2024-01-01 10:00:00 |
| update_by | varchar(64) | NULL | YES | 更新人 | system |
| update_time | datetime | CURRENT_TIMESTAMP | YES | 更新时间 | 2024-01-01 10:05:00 |
| interpretation_mode | varchar(1) | '0' | YES | 解读模式 | 0=抽牌模式, 1=自选牌模式 |
| error_msg | varchar(1024) | NULL | YES | 异常信息 | AI服务暂时不可用 |
| deleted | tinyint(1) | 0 | NOT NULL | 软删除标识 | 0=正常, 1=已删除 |

**业务逻辑**:
- draw_result以JSON格式存储抽牌结果
- interpretation_mode区分随机抽牌和用户自选牌
- status跟踪解读状态，支持异步处理
- deleted支持软删除，保护用户隐私

#### 2.4.4 塔罗每日洞察表 (tarot_daily_insight)
**表用途**: 为用户提供每日塔罗洞察

| 字段名 | 类型 | 默认值 | 是否为空 | 说明 | 枚举值/示例 |
|--------|------|--------|----------|------|-------------|
| id | int | AUTO_INCREMENT | NOT NULL | 主键 | 1, 2, 3... |
| user_id | int(20) | - | NOT NULL | 用户ID | 123 |
| open_id | varchar(64) | NULL | YES | 微信OpenID | oABC123... |
| card_id | int(20) | - | NOT NULL | 今日牌面ID | 1 |
| position | varchar(10) | - | NOT NULL | 正逆位 | upright, reversed |
| lucky_color | varchar(255) | NULL | YES | 幸运色 | 红色, 蓝色 |
| lucky_number | varchar(255) | NULL | YES | 幸运数字 | 7, 13 |
| create_by | varchar(64) | NULL | YES | 创建人 | system |
| create_time | datetime | CURRENT_TIMESTAMP | YES | 创建时间 | 2024-01-01 00:00:00 |
| update_by | varchar(64) | NULL | YES | 更新人 | system |
| update_time | datetime | CURRENT_TIMESTAMP | YES | 更新时间 | 2024-01-01 00:01:00 |
| insight_date | varchar(20) | NULL | YES | 洞察日期 | 2024-01-01 |

**索引信息**:
- PRIMARY KEY: id
- UNIQUE KEY: (user_id, insight_date)

**业务逻辑**:
- 每日为用户生成一张塔罗牌洞察
- position随机设置正位或逆位
- lucky_color和lucky_number提供额外的运势信息
- insight_date确保每用户每日只有一条记录

### 2.5 系统管理表

#### 2.5.1 系统配置表 (sys_config)
**表用途**: 存储系统运行参数和开关配置

| 字段名 | 类型 | 默认值 | 是否为空 | 说明 | 枚举值/示例 |
|--------|------|--------|----------|------|-------------|
| config_id | int(5) | AUTO_INCREMENT | NOT NULL | 配置ID | 1, 2, 3... |
| config_name | varchar(100) | '' | YES | 参数名称 | GPT功能开关 |
| config_key | varchar(100) | '' | YES | 参数键名 | gpt_switch |
| config_value | varchar(500) | '' | YES | 参数值 | 1, 0, 10 |
| config_type | char(1) | 'N' | YES | 系统内置 | Y=是, N=否 |
| create_by | varchar(64) | '' | YES | 创建者 | admin |
| create_time | datetime | CURRENT_TIMESTAMP | YES | 创建时间 | 2024-01-01 10:00:00 |
| update_by | varchar(64) | '' | YES | 更新者 | admin |
| update_time | datetime | CURRENT_TIMESTAMP | YES | 更新时间 | 2024-01-01 11:00:00 |
| remark | varchar(500) | NULL | YES | 备注说明 | 控制GPT功能是否可用 |

**索引信息**:
- PRIMARY KEY: config_id
- UNIQUE KEY: config_key

**常用配置项**:
- `gpt_switch`: GPT功能开关 (1=开启, 0=关闭)
- `gpt_consume_points`: GPT对话消耗积分数 (默认10)
- `draw_consume_points`: AI绘画消耗积分数 (默认20)
- `sign_reward_points`: 签到奖励积分数 (默认10)
- `tarot_bestow`: 塔罗牌赠送配置 (JSON格式)
- `first_url`: 首次使用引导图片URL
- `max_file_size`: 文件上传最大大小 (MB)
- `api_rate_limit`: API调用频率限制 (次/分钟)

#### 2.5.2 系统操作日志表 (sys_oper_log)
**表用途**: 记录系统操作审计日志

| 字段名 | 类型 | 默认值 | 是否为空 | 说明 | 枚举值/示例 |
|--------|------|--------|----------|------|-------------|
| oper_id | bigint(20) | AUTO_INCREMENT | NOT NULL | 日志主键 | 1, 2, 3... |
| title | varchar(50) | '' | YES | 模块标题 | 用户管理, 系统配置 |
| business_type | int(2) | 0 | YES | 业务类型 | 0=其它, 1=新增, 2=修改, 3=删除 |
| method | varchar(100) | '' | YES | 方法名称 | login, register |
| request_method | varchar(10) | '' | YES | 请求方式 | GET, POST, PUT, DELETE |
| operator_type | int(1) | 0 | YES | 操作类别 | 0=其它, 1=后台用户, 2=手机端用户 |
| oper_name | varchar(50) | '' | YES | 操作人员 | admin, user_123 |
| dept_name | varchar(50) | '' | YES | 部门名称 | 技术部 |
| oper_url | varchar(255) | '' | YES | 请求URL | /api/user/login |
| oper_ip | varchar(128) | '' | YES | 主机地址 | *********** |
| oper_location | varchar(255) | '' | YES | 操作地点 | 内网IP |
| oper_param | varchar(2000) | '' | YES | 请求参数 | {"username":"admin"} |
| json_result | varchar(2000) | '' | YES | 返回参数 | {"code":200,"msg":"success"} |
| status | int(1) | 0 | YES | 操作状态 | 0=正常, 1=异常 |
| error_msg | varchar(2000) | '' | YES | 错误消息 | 用户名或密码错误 |
| oper_time | datetime | NULL | YES | 操作时间 | 2024-01-01 10:00:00 |

**索引信息**:
- PRIMARY KEY: oper_id
- INDEX: business_type, oper_time, status

**业务逻辑**:
- 通过AOP切面自动记录关键操作
- business_type区分操作类型，便于统计分析
- operator_type区分操作来源
- 敏感参数需要脱敏处理

#### 2.5.3 异常日志表 (exception_log)
**表用途**: 记录系统异常和错误信息

| 字段名 | 类型 | 默认值 | 是否为空 | 说明 | 枚举值/示例 |
|--------|------|--------|----------|------|-------------|
| id | bigint | AUTO_INCREMENT | NOT NULL | 主键 | 1, 2, 3... |
| trace_id | varchar(64) | NULL | YES | 链路追踪ID | trace_123456 |
| user_id | varchar(64) | NULL | YES | 用户ID | user_123 |
| exception_name | varchar(255) | NULL | YES | 异常名称 | NullPointerException |
| exception_message | text | NULL | YES | 异常信息 | Cannot invoke method on null object |
| stack_trace | longtext | NULL | YES | 异常堆栈 | java.lang.NullPointerException... |
| request_uri | varchar(255) | NULL | YES | 请求URI | /api/chat/message |
| request_method | varchar(10) | NULL | YES | 请求方法 | POST |
| request_params | text | NULL | YES | 请求参数 | {"message":"hello"} |
| user_agent | varchar(500) | NULL | YES | 用户代理 | Mozilla/5.0... |
| ip_address | varchar(50) | NULL | YES | IP地址 | *********** |
| create_time | datetime | CURRENT_TIMESTAMP | YES | 创建时间 | 2024-01-01 10:00:00 |

**业务逻辑**:
- trace_id用于分布式系统链路追踪
- stack_trace存储完整异常堆栈，便于问题定位
- 自动记录请求上下文信息
- 支持异常统计和告警

## 3. 数据库设计规范和约定

### 3.1 字段类型规范

#### 3.1.1 主键设计
- **类型**: int unsigned AUTO_INCREMENT 或 bigint AUTO_INCREMENT
- **命名**: 统一使用 `id` 作为主键名
- **选择原则**: 预估数据量 < 1000万用int，否则用bigint

#### 3.1.2 时间字段设计
- **类型**: datetime
- **默认值**: CURRENT_TIMESTAMP()
- **更新**: ON UPDATE CURRENT_TIMESTAMP() (仅update_time)
- **命名**: create_time, update_time

#### 3.1.3 状态字段设计
- **类型**: int(1) 或 tinyint
- **默认值**: 0
- **含义**: 0=正常/启用，1=异常/禁用，2=特殊状态，9=删除/下架

#### 3.1.4 金额字段设计
- **类型**: decimal(15,2) 或 decimal(12,2)
- **精度**: 支持大额交易，保留2位小数
- **币种**: 单独字段存储币种代码

#### 3.1.5 文本字段设计
- **短文本**: varchar(100) - 用户名、标题等
- **中文本**: varchar(255) 或 varchar(500) - 描述、URL等
- **长文本**: text - 内容、配置等
- **超长文本**: longtext - 大量数据存储

### 3.2 索引设计规范

#### 3.2.1 主键索引
- 每张表必须有主键
- 主键字段建议使用自增整型

#### 3.2.2 唯一索引
- 业务唯一字段建立唯一索引
- 如：账号、邮箱、订单号等

#### 3.2.3 普通索引
- 外键字段建立索引
- 查询频繁的字段建立索引
- 状态、类型等筛选字段建立索引

#### 3.2.4 复合索引
- 多字段联合查询建立复合索引
- 注意字段顺序，遵循最左前缀原则

### 3.3 命名规范

#### 3.3.1 表命名
- 小写字母 + 下划线
- 见名知意，体现业务含义
- 如：chat_message, tarot_spread

#### 3.3.2 字段命名
- 小写字母 + 下划线
- 避免使用保留字
- 布尔类型字段使用 is_ 前缀

#### 3.3.3 索引命名
- 主键索引：PRIMARY
- 唯一索引：uk_ + 字段名
- 普通索引：idx_ + 字段名
- 复合索引：idx_ + 主要字段名

### 3.4 注释规范

#### 3.4.1 表注释
- 每张表必须有中文注释
- 说明表的业务用途
- 如：COMMENT '聊天消息表'

#### 3.4.2 字段注释
- 每个字段必须有中文注释
- 枚举字段说明各值含义
- 如：COMMENT '状态(0:正常 1:禁用)'

### 3.5 性能优化建议

#### 3.5.1 表结构优化
- 合理选择字段类型，避免浪费存储空间
- 经常一起查询的字段考虑放在同一张表
- 大表考虑垂直拆分

#### 3.5.2 索引优化
- 避免过多索引，影响写入性能
- 定期分析慢查询，优化索引
- 考虑使用覆盖索引减少回表

#### 3.5.3 查询优化
- 避免SELECT *，只查询需要的字段
- 合理使用LIMIT分页
- 大数据量查询考虑使用游标分页

#### 3.5.4 维护建议
- 定期清理历史数据
- 监控表大小和索引效率
- 及时处理碎片整理
