# 超级智能社(SuperAI)需求设计说明书

## 1. 项目背景与目标

### 1.1 项目背景
随着人工智能技术的快速发展，AI聊天、AI绘画、AI占卜等应用场景日益丰富。超级智能社项目旨在构建一个集成多种AI功能的综合性平台，为用户提供智能对话、创意绘画、塔罗占卜等服务。

### 1.2 项目目标
- 构建稳定可靠的AI服务平台
- 提供多样化的AI应用场景
- 建立完善的用户管理和支付体系
- 支持多端访问和第三方集成
- 实现商业化运营和盈利模式

### 1.3 目标用户
- **个人用户**: 对AI技术感兴趣的普通用户
- **创作者**: 需要AI辅助创作的内容创作者
- **占卜爱好者**: 对塔罗牌占卜感兴趣的用户
- **企业用户**: 需要AI服务的小微企业

## 2. 系统功能需求

### 2.1 用户管理需求

#### 2.1.1 用户注册登录
- **需求描述**: 用户可通过多种方式注册和登录系统
- **功能要求**:
  - 支持手机号、邮箱注册
  - 支持Facebook、Google、微信第三方登录
  - 提供验证码验证机制
  - 支持记住登录状态
- **业务规则**:
  - 手机号/邮箱唯一性校验
  - 密码强度要求(8位以上，包含字母数字)
  - 验证码5分钟有效期
  - 登录失败5次锁定账号30分钟

#### 2.1.2 用户信息管理
- **需求描述**: 用户可查看和修改个人信息
- **功能要求**:
  - 查看个人基本信息
  - 修改昵称、头像、邮箱等
  - 修改登录密码
  - 查看账号安全信息
- **业务规则**:
  - 昵称长度2-20字符
  - 头像支持jpg/png格式，最大2MB
  - 修改密码需要验证原密码

### 2.2 聊天功能需求

#### 2.2.1 AI对话功能
- **需求描述**: 用户可与AI进行智能对话
- **功能要求**:
  - 创建聊天会话
  - 实时流式对话
  - 支持多种AI模型切换
  - 查看历史对话记录
- **业务规则**:
  - 每次对话消耗指定积分
  - 单次对话最大4000字符
  - 历史记录保存30天
  - 敏感内容过滤

#### 2.2.2 智能体功能
- **需求描述**: 提供预设的专业AI智能体
- **功能要求**:
  - 智能体列表展示
  - 智能体分类筛选
  - 智能体详情查看
  - 自定义智能体创建
- **业务规则**:
  - 智能体按热度排序
  - 付费智能体需要权限
  - 自定义智能体需要审核

#### 2.2.3 图片生成功能
- **需求描述**: 用户可通过文字描述生成图片
- **功能要求**:
  - 文字转图片生成
  - 多种画风选择
  - 图片质量设置
  - 生成历史记录
- **业务规则**:
  - 每次生成消耗指定积分
  - 图片生成时间3-5分钟
  - 支持1024x1024分辨率
  - 违规内容检测

### 2.3 塔罗牌功能需求

#### 2.3.1 塔罗牌阵
- **需求描述**: 提供多种塔罗牌阵供用户选择
- **功能要求**:
  - 牌阵列表展示
  - 牌阵详情介绍
  - 牌阵布局图展示
  - 牌阵消耗积分显示
- **业务规则**:
  - 不同牌阵消耗不同积分
  - 牌阵按复杂度分类
  - 新手推荐简单牌阵

#### 2.3.2 抽牌解读
- **需求描述**: 用户可进行塔罗牌抽取和解读
- **功能要求**:
  - 随机抽取塔罗牌
  - 正逆位随机设置
  - AI专业解读
  - 解读结果保存
- **业务规则**:
  - 每次解读消耗指定积分
  - 解读结果包含牌意和建议
  - 解读历史永久保存
  - 每日免费解读次数限制

#### 2.3.3 每日洞察
- **需求描述**: 为用户提供每日塔罗洞察
- **功能要求**:
  - 每日自动生成洞察
  - 个性化洞察内容
  - 洞察历史查看
  - 分享功能
- **业务规则**:
  - 每日0点更新洞察
  - 洞察内容基于用户画像
  - 免费功能，无积分消耗

### 2.4 支付功能需求

#### 2.4.1 产品管理
- **需求描述**: 管理可购买的产品和套餐
- **功能要求**:
  - 产品列表展示
  - 产品详情介绍
  - 价格和优惠显示
  - 产品分类筛选
- **业务规则**:
  - 产品按类型分类(积分、会员、次数)
  - 支持限时优惠
  - 新用户专享价格

#### 2.4.2 支付下单
- **需求描述**: 用户可通过多种方式支付购买
- **功能要求**:
  - 微信支付下单
  - 支付宝支付下单
  - SE支付(越南本地)
  - 订单状态查询
- **业务规则**:
  - 订单15分钟内有效
  - 支付成功自动发货
  - 支持部分退款
  - 异常订单人工处理

#### 2.4.3 充值记录
- **需求描述**: 用户可查看充值和消费记录
- **功能要求**:
  - 充值记录查询
  - 消费记录查询
  - 余额变动明细
  - 导出功能
- **业务规则**:
  - 记录永久保存
  - 支持时间范围筛选
  - 敏感信息脱敏显示

### 2.5 积分系统需求

#### 2.5.1 积分管理
- **需求描述**: 管理用户积分的获取和消耗
- **功能要求**:
  - 积分余额查询
  - 积分获取记录
  - 积分消耗记录
  - 积分有效期管理
- **业务规则**:
  - 积分不可转让
  - 积分有效期1年
  - 过期积分自动清理
  - 积分消耗按先进先出

#### 2.5.2 积分获取
- **需求描述**: 用户可通过多种方式获取积分
- **功能要求**:
  - 充值购买积分
  - 签到获得积分
  - 邀请好友获得积分
  - 活动奖励积分
- **业务规则**:
  - 每日签到奖励递增
  - 邀请好友双方都有奖励
  - 活动积分有上限
  - 异常获取积分风控

### 2.6 签到功能需求

#### 2.6.1 每日签到
- **需求描述**: 用户可进行每日签到获得奖励
- **功能要求**:
  - 每日签到操作
  - 签到状态查询
  - 签到奖励发放
  - 连续签到统计
- **业务规则**:
  - 每日只能签到一次
  - 连续签到奖励递增
  - 断签重新计算连续天数
  - 使用分布式锁防重复

#### 2.6.2 签到记录
- **需求描述**: 用户可查看签到历史记录
- **功能要求**:
  - 签到日历展示
  - 签到奖励记录
  - 连续签到统计
  - 签到排行榜
- **业务规则**:
  - 记录保存3个月
  - 支持月度统计
  - 排行榜每日更新

## 3. 系统非功能需求

### 3.1 性能需求
- **响应时间**: 接口响应时间<2秒
- **并发用户**: 支持1000并发用户
- **数据库**: 支持10万级用户数据
- **文件存储**: 支持TB级文件存储

### 3.2 可用性需求
- **系统可用性**: 99.5%以上
- **故障恢复**: 故障恢复时间<30分钟
- **数据备份**: 每日自动备份
- **容灾机制**: 支持异地容灾

### 3.3 安全需求
- **数据加密**: 敏感数据加密存储
- **传输安全**: HTTPS传输
- **访问控制**: 基于角色的权限控制
- **审计日志**: 完整的操作审计

### 3.4 兼容性需求
- **浏览器**: 支持Chrome、Safari、Firefox
- **移动端**: 支持iOS、Android
- **微信**: 支持微信小程序
- **API**: 提供RESTful API

## 4. 业务流程设计

### 4.1 用户注册流程
```
用户输入注册信息 → 验证码验证 → 信息校验 → 创建账号 → 发送欢迎消息 → 注册完成
```

### 4.2 AI对话流程
```
选择智能体 → 创建聊天室 → 输入问题 → 积分扣减 → AI处理 → 流式返回 → 保存记录
```

### 4.3 支付购买流程
```
选择产品 → 创建订单 → 选择支付方式 → 支付处理 → 支付回调 → 发货处理 → 购买完成
```

### 4.4 塔罗解读流程
```
选择牌阵 → 积分扣减 → 随机抽牌 → AI解读 → 返回结果 → 保存记录
```

## 5. 数据设计要求

### 5.1 数据存储
- 用户数据存储在MySQL主库
- 会话数据存储在Redis缓存
- 文件数据存储在对象存储
- 日志数据存储在日志系统

### 5.2 数据安全
- 密码使用BCrypt加密
- 敏感信息使用AES加密
- 数据传输使用HTTPS
- 定期数据备份和恢复测试

### 5.3 数据一致性
- 关键业务使用事务保证
- 分布式锁保证并发安全
- 异步任务保证最终一致性
- 定期数据校验和修复
