# 超级智能社(SuperAI)技术架构说明书

## 1. 项目概述

### 1.1 项目简介
超级智能社(SuperAI)是一个基于Spring Boot的智能AI聊天平台，集成了ChatGPT、Midjourney绘画、塔罗牌占卜等多种AI功能，支持微信支付、支付宝支付、SE支付(越南本地支付)等多种支付方式，提供完整的用户管理、积分系统、签到功能等。项目主要面向越南市场，支持多语言国际化。

### 1.2 技术栈详细
- **后端框架**: Spring Boot 2.7.x
- **数据库**: MySQL 8.0 (MariaDB兼容)
- **缓存**: Redis 6.x + Redisson分布式锁
- **ORM框架**: MyBatis-Plus 3.5.x
- **权限认证**: Sa-Token 1.34.x
- **API文档**: Swagger 3.0 (OpenAPI 3)
- **消息队列**: RocketMQ (可选配置)
- **支付集成**: 微信支付V3、支付宝、SE支付、Momo支付
- **第三方登录**: Facebook OAuth2、Google OAuth2、微信小程序
- **AI集成**: OpenAI GPT-3.5/4、Midjourney Discord Bot、自定义智能体
- **国际化**: 支持中文、英文、越南语
- **部署**: Docker容器化 + Nginx反向代理
- **监控**: 自定义日志系统 + 操作审计

## 2. 系统架构

### 2.1 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端应用层     │    │   移动端应用     │    │   第三方集成     │
│  (Web/H5)      │    │  (微信小程序)    │    │ (OpenAI/MJ等)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
┌─────────────────────────────────────────────────────────────────┐
│                        API网关层                                │
│                   (Spring Boot Controller)                     │
└─────────────────────────────────────────────────────────────────┘
         │
┌─────────────────────────────────────────────────────────────────┐
│                        业务服务层                               │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│  │  聊天服务   │ │  支付服务   │ │  用户服务   │ │  塔罗服务   │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│  │  智能体服务 │ │  积分服务   │ │  签到服务   │ │  配置服务   │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
└─────────────────────────────────────────────────────────────────┘
         │
┌─────────────────────────────────────────────────────────────────┐
│                        数据访问层                               │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│  │   MySQL     │ │    Redis    │ │  文件存储   │ │   日志存储  │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

### 2.2 模块架构
- **chatgpt-api**: 核心API模块，包含所有业务逻辑
- **chatgpt-manage**: 管理后台模块(可选)

### 2.3 详细包结构
```
com.hncboy.chatgpt
├── ChatGptApplication.java                    # 主启动类
├── front                                      # 前端业务模块
│   ├── controller                            # 控制器层
│   │   ├── chat                              # 聊天功能
│   │   │   ├── ChatController.java           # 聊天室管理
│   │   │   ├── ChatMessageController.java    # 消息处理
│   │   │   └── ChatTransferController.java   # 聊天转接
│   │   └── common                            # 通用功能
│   │       ├── AuthController.java           # 认证授权
│   │       ├── IntelligentAgentController.java # 智能体管理
│   │       ├── WriteAgentController.java     # 写作应用
│   │       ├── AlOrdersController.java       # 支付宝订单
│   │       ├── CategoryInfoController.java   # 分类管理
│   │       ├── SysConfigController.java      # 系统配置
│   │       └── JobController.java            # 定时任务
│   ├── service                               # 服务层
│   │   ├── PayService.java                   # 支付服务接口
│   │   ├── ChatMessageService.java           # 聊天消息服务
│   │   ├── IntelligentAgentService.java      # 智能体服务
│   │   ├── UserBaseInfoService.java          # 用户基础服务
│   │   ├── ExceptionLogService.java          # 异常日志服务
│   │   └── impl                              # 服务实现类
│   │       ├── PayServiceImpl.java           # 支付服务实现
│   │       ├── ChatMessageServiceImpl.java   # 聊天消息实现
│   │       ├── IntelligentAgentServiceImpl.java # 智能体实现
│   │       └── ...                           # 其他服务实现
│   ├── mapper                                # 数据访问层
│   │   ├── UserBaseInfoMapper.java           # 用户数据访问
│   │   ├── ChatMessageMapper.java            # 聊天消息数据访问
│   │   ├── IntelligentAgentMapper.java       # 智能体数据访问
│   │   ├── WriteAgentMapper.java             # 写作应用数据访问
│   │   └── ...                               # 其他Mapper接口
│   ├── framework                             # 框架核心
│   │   ├── config                            # 配置类
│   │   │   ├── RedisConfig.java              # Redis配置
│   │   │   ├── RedissonConfig.java           # Redisson配置
│   │   │   ├── SaTokenConfig.java            # Sa-Token配置
│   │   │   ├── ChatConfig.java               # 聊天配置
│   │   │   ├── CustomCorsConfiguration.java  # 跨域配置
│   │   │   ├── JacksonConfiguration.java     # JSON配置
│   │   │   └── MyBatisMetaObjectConfig.java  # MyBatis配置
│   │   ├── domain                            # 领域对象
│   │   │   ├── entity                        # 实体类
│   │   │   │   ├── UserBaseInfo.java         # 用户基础信息实体
│   │   │   │   ├── ChatMessageDO.java        # 聊天消息实体
│   │   │   │   ├── ChatRoomDO.java           # 聊天室实体
│   │   │   │   ├── IntelligentAgent.java     # 智能体实体
│   │   │   │   ├── Product.java              # 产品实体
│   │   │   │   ├── Orders.java               # 订单实体
│   │   │   │   └── ...                       # 其他实体类
│   │   │   ├── dto                           # 数据传输对象
│   │   │   │   ├── ChatProcessV2Request.java # 聊天处理请求
│   │   │   │   ├── IntelligentAgentDTO.java  # 智能体DTO
│   │   │   │   ├── PayCodeDTO.java           # 支付码DTO
│   │   │   │   ├── SysConfigDTO.java         # 系统配置DTO
│   │   │   │   └── ...                       # 其他DTO类
│   │   │   └── vo                            # 视图对象
│   │   │       ├── UserBaseInfoVO.java       # 用户信息VO
│   │   │       ├── IntelligentAgentVO.java   # 智能体VO
│   │   │       ├── WxUserInfoVO.java         # 微信用户VO
│   │   │       ├── ProductVo.java            # 产品VO
│   │   │       └── ...                       # 其他VO类
│   │   ├── handler                           # 处理器
│   │   │   ├── aspect                        # AOP切面
│   │   │   │   ├── WebLogAspect.java         # Web日志切面
│   │   │   │   └── FrontPreAuthAspect.java   # 权限认证切面
│   │   │   ├── response                      # 响应处理
│   │   │   │   ├── R.java                    # 统一响应格式
│   │   │   │   └── ResultCode.java           # 响应码枚举
│   │   │   └── ...                           # 其他处理器
│   │   ├── exception                         # 异常处理
│   │   │   ├── RestExceptionTranslator.java  # 全局异常处理
│   │   │   ├── ServiceException.java         # 业务异常
│   │   │   ├── AuthException.java            # 认证异常
│   │   │   └── BalanceException.java         # 余额异常
│   │   ├── enums                             # 枚举类
│   │   │   ├── MemberEnum.java               # 会员类型枚举
│   │   │   ├── EnableDisableStatusEnum.java  # 启用禁用枚举
│   │   │   ├── OrderStatusEnum.java          # 订单状态枚举
│   │   │   └── GroupsEnum.java               # 分组枚举
│   │   ├── constant                          # 常量类
│   │   │   ├── ApplicationConstant.java      # 应用常量
│   │   │   └── OrderConstant.java            # 订单常量
│   │   └── util                              # 工具类
│   │       ├── CurrentUserUtil.java          # 当前用户工具
│   │       ├── SignInUtils.java              # 签到工具
│   │       ├── WebUtil.java                  # Web工具
│   │       └── ...                           # 其他工具类
│   ├── helper                                # 业务助手类
│   │   ├── ChatMsgBuildHelper.java           # 聊天消息构建助手
│   │   ├── ChatMsgV2BuildHelper.java         # 聊天消息V2助手
│   │   ├── OpenAiMsgHelper.java              # OpenAI消息助手
│   │   ├── LoginManager.java                 # 登录管理助手
│   │   ├── HttpUrlTransferHelper.java        # HTTP转发助手
│   │   └── InviteTreeHelper.java             # 邀请树助手
│   ├── handler                               # 消息处理器
│   │   └── mp                                # 微信公众号处理
│   │       ├── handler                       # 消息处理器
│   │       │   ├── MsgHandler2.java          # 消息处理器2
│   │       │   ├── MsgHandler3.java          # 消息处理器3
│   │       │   └── MsgHandler4.java          # 消息处理器4
│   │       └── builder                       # 消息构建器
│   │           ├── TextBuilder.java          # 文本消息构建
│   │           └── NewsBuilder.java          # 图文消息构建
│   └── momo                                  # Momo支付集成
│       └── MomoPaymentService.java           # Momo支付服务
└── tarot                                     # 塔罗牌业务模块
    ├── controller                            # 塔罗牌控制器
    │   ├── tarot                             # 塔罗牌功能
    │   │   ├── TarotSpreadController.java    # 牌阵控制器
    │   │   ├── TarotCardMeaningController.java # 牌义控制器
    │   │   ├── TarotReadingRecordController.java # 解读记录控制器
    │   │   ├── TarotDailyInsightController.java # 每日洞察控制器
    │   │   └── UserCheckInRecordController.java # 签到记录控制器
    │   └── ThirdAuthController.java          # 第三方认证控制器
    ├── service                               # 塔罗牌服务
    │   ├── TarotSpreadService.java           # 牌阵服务
    │   ├── TarotCardMeaningService.java      # 牌义服务
    │   ├── TarotReadingRecordService.java    # 解读记录服务
    │   ├── TarotDailyInsightService.java     # 每日洞察服务
    │   ├── UserCheckInRecordService.java     # 签到记录服务
    │   ├── FacebookAuthService.java          # Facebook认证服务
    │   ├── GoogleAuthService.java            # Google认证服务
    │   ├── FingerprintByBrowserAuthService.java # 浏览器指纹服务
    │   ├── UserService.java                  # 塔罗用户服务
    │   └── impl                              # 服务实现
    │       └── ...                           # 各服务实现类
    ├── domain                                # 塔罗牌领域对象
    │   ├── entity                            # 实体类
    │   │   ├── User.java                     # 塔罗用户实体
    │   │   ├── TarotSpread.java              # 牌阵实体
    │   │   ├── TarotCardMeaning.java         # 牌义实体
    │   │   ├── TarotReadingRecord.java       # 解读记录实体
    │   │   ├── TarotDailyInsight.java        # 每日洞察实体
    │   │   └── UserCheckInRecord.java        # 签到记录实体
    │   ├── dto                               # DTO类
    │   │   ├── TarotSpreadDTO.java           # 牌阵DTO
    │   │   └── ...                           # 其他DTO
    │   └── vo                                # VO类
    │       ├── TarotCardMeaningVO.java       # 牌义VO
    │       └── ...                           # 其他VO
    ├── mapper                                # 数据访问层
    │   ├── TarotSpreadMapper.java            # 牌阵数据访问
    │   ├── TarotCardMeaningMapper.java       # 牌义数据访问
    │   ├── UserMapper.java                   # 塔罗用户数据访问
    │   └── ...                               # 其他Mapper
    └── test                                  # 测试类
        └── TarotTest.java                    # 塔罗功能测试
```

## 3. 核心组件

### 3.1 认证授权
- **Sa-Token**: 提供登录认证、权限校验
- **@FrontPreAuth**: 前端接口权限控制注解
- **@IgnoreAuth**: 忽略权限校验注解
- **CurrentUserUtil**: 当前用户工具类

### 3.2 数据库配置
- **主库**: MySQL 8.0
- **连接池**: HikariCP
- **ORM**: MyBatis-Plus
- **分页**: MyBatis-Plus分页插件

### 3.3 缓存配置
- **Redis**: 用于缓存、分布式锁、会话存储
- **Redisson**: 分布式锁实现
- **RedisTemplate**: Redis操作模板

### 3.4 日志配置
- **Web日志**: WebLogAspect AOP切面记录
- **操作日志**: SysOperLog数据库存储
- **异常日志**: ExceptionLog异常信息记录

### 3.5 异常处理
- **RestExceptionTranslator**: 全局异常处理器
- **ServiceException**: 业务异常
- **AuthException**: 认证异常
- **BalanceException**: 余额异常

## 4. 配置管理

### 4.1 环境配置
- **dev**: 开发环境
- **test**: 测试环境  
- **prod**: 生产环境

### 4.2 核心配置
- **服务端口**: 3082
- **上下文路径**: /super/ai
- **文件上传**: 最大30MB
- **数据库**: 多环境配置
- **Redis**: 集群配置支持

### 4.3 第三方配置
- **微信小程序**: appid、secret配置
- **微信支付**: 商户号、密钥、证书配置
- **支付宝**: 应用ID、公钥、私钥配置
- **OpenAI**: 多通道API配置
- **Midjourney**: Discord集成配置

## 5. 安全机制

### 5.1 接口安全
- Sa-Token会话管理
- CORS跨域配置
- 请求参数校验
- SQL注入防护

### 5.2 数据安全
- 密码加密存储
- 敏感信息脱敏
- 数据库连接加密
- Redis密码保护

### 5.3 业务安全
- 接口限流
- 重复提交防护
- 分布式锁机制
- 异常监控告警

## 6. 性能优化

### 6.1 数据库优化
- 索引优化
- 分页查询
- 连接池配置
- 慢查询监控

### 6.2 缓存优化
- Redis缓存策略
- 热点数据缓存
- 缓存穿透防护
- 缓存雪崩防护

### 6.3 接口优化
- 异步处理
- 批量操作
- 数据压缩
- CDN加速

## 7. 监控运维

### 7.1 应用监控
- 接口响应时间
- 错误率统计
- 系统资源监控
- 业务指标监控

### 7.2 日志管理
- 分级日志记录
- 日志文件轮转
- 异常日志告警
- 操作审计日志

### 7.3 部署方案
- Docker容器化
- 负载均衡
- 蓝绿部署
- 自动化运维

## 8. 扩展性设计

### 8.1 模块化设计
- 业务模块独立
- 接口标准化
- 配置外部化
- 组件可插拔

### 8.2 微服务准备
- 服务拆分边界清晰
- 数据库分离
- 配置中心支持
- 服务注册发现

### 8.3 多租户支持
- 数据隔离
- 配置隔离
- 资源隔离
- 权限隔离
