# 超级智能社(SuperAI)需求详细设计说明书

## 1. 系统架构详细设计

### 1.1 分层架构设计

#### 1.1.1 控制器层(Controller Layer)详细设计
```java
// 基础控制器 - 提供统一的响应处理
@RestController
@RequestMapping("/super/ai")
@Slf4j
public abstract class BaseController {

    /**
     * 成功响应
     */
    protected <T> R<T> success(T data) {
        return R.success(data);
    }

    /**
     * 失败响应
     */
    protected <T> R<T> fail(String message) {
        return R.fail(message);
    }
}

// 权限控制注解详细说明
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface FrontPreAuth {
    /**
     * 是否需要登录，默认true
     */
    boolean required() default true;

    /**
     * 权限码，用于细粒度权限控制
     */
    String[] permissions() default {};
}

@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface IgnoreAuth {
    /**
     * 忽略权限校验的原因说明
     */
    String reason() default "";
}

// 聊天控制器实现示例
@RestController
@RequestMapping("/super/ai")
@FrontPreAuth
@Slf4j
public class ChatController extends BaseController {

    @Autowired
    private ChatRoomService chatRoomService;

    @Autowired
    private ChatMessageService chatMessageService;

    /**
     * 创建聊天室
     * 业务逻辑：创建新的聊天会话，支持指定智能体和模型
     */
    @PostMapping("/createChatRoom")
    public R<ChatRoomDO> createChatRoom(@RequestBody ChatRoomDO chatRoomDO) {
        // 1. 参数校验
        if (StringUtils.isBlank(chatRoomDO.getTitle())) {
            chatRoomDO.setTitle("新对话");
        }

        // 2. 设置用户ID
        chatRoomDO.setUserId(CurrentUserUtil.getUserId());

        // 3. 创建聊天室
        ChatRoomDO result = chatRoomService.createChatRoom(chatRoomDO);

        return success(result);
    }

    /**
     * 获取聊天室列表
     * 业务逻辑：分页查询用户的聊天室，支持类型筛选
     */
    @GetMapping("/getChatRoom")
    public R<IPage<ChatRoomDO>> getChatRoom(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String type) {

        Page<ChatRoomDO> page = new Page<>(current, size);
        LambdaQueryWrapper<ChatRoomDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ChatRoomDO::getUserId, CurrentUserUtil.getUserId())
               .eq(ChatRoomDO::getStatus, 0); // 只查询未删除的

        if (StringUtils.isNotBlank(type)) {
            wrapper.eq(ChatRoomDO::getType, type);
        }

        wrapper.orderByDesc(ChatRoomDO::getUpdateTime);

        IPage<ChatRoomDO> result = chatRoomService.page(page, wrapper);
        return success(result);
    }

    /**
     * 删除聊天室
     * 业务逻辑：软删除聊天室，不删除历史消息
     */
    @DeleteMapping("/deleteRoom/{roomId}")
    public R<String> deleteRoom(@PathVariable Integer roomId) {
        // 1. 权限校验 - 只能删除自己的聊天室
        ChatRoomDO chatRoom = chatRoomService.getById(roomId);
        if (chatRoom == null) {
            return fail("聊天室不存在");
        }

        if (!chatRoom.getUserId().equals(CurrentUserUtil.getUserId())) {
            return fail("无权限删除此聊天室");
        }

        // 2. 软删除
        chatRoom.setStatus(1);
        chatRoom.setUpdateTime(new Date());
        chatRoomService.updateById(chatRoom);

        return success("删除成功");
    }
}
```

#### 1.1.2 服务层(Service Layer)
```java
// 业务接口定义
public interface ChatMessageService {
    // 业务方法定义
    // 事务控制
    // 缓存策略
}

// 业务实现类
@Service
@Transactional
public class ChatMessageServiceImpl implements ChatMessageService {
    // 具体业务逻辑
    // 数据校验
    // 异常处理
}
```

#### 1.1.3 数据访问层(Mapper Layer)
```java
// MyBatis-Plus基础接口
@Mapper
public interface UserBaseInfoMapper extends BaseMapper<UserBaseInfo> {
    // 自定义SQL方法
    // 复杂查询
    // 统计分析
}
```

### 1.2 核心组件设计

#### 1.2.1 认证授权组件
```java
// Sa-Token配置
@Configuration
public class SaTokenConfig {
    // Token配置
    // 权限校验
    // 会话管理
}

// 权限切面
@Aspect
@Component
public class FrontPreAuthAspect {
    // 权限检查逻辑
    // 登录状态验证
    // 异常处理
}
```

#### 1.2.2 缓存组件
```java
// Redis配置
@Configuration
public class RedisConfig {
    // 连接池配置
    // 序列化配置
    // 过期策略
}

// 分布式锁
@Component
public class RedisLockUtil {
    // 加锁逻辑
    // 解锁逻辑
    // 超时处理
}
```

## 2. 核心业务详细设计

### 2.1 用户管理模块详细设计

#### 2.1.1 用户注册业务逻辑
```java
@Override
public R<String> register(UserRegisterDTO dto) {
    // 1. 参数校验
    if (StringUtils.isBlank(dto.getAccount())) {
        throw new ServiceException("账号不能为空");
    }
    
    // 2. 验证码校验
    String cacheCode = redisService.get("verify_code:" + dto.getAccount());
    if (!dto.getVerifyCode().equals(cacheCode)) {
        throw new ServiceException("验证码错误");
    }
    
    // 3. 账号唯一性校验
    LambdaQueryWrapper<UserBaseInfo> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(UserBaseInfo::getAccount, dto.getAccount());
    if (this.count(wrapper) > 0) {
        throw new ServiceException("账号已存在");
    }
    
    // 4. 创建用户
    UserBaseInfo user = new UserBaseInfo();
    user.setAccount(dto.getAccount());
    user.setPassword(BCrypt.hashpw(dto.getPassword(), BCrypt.gensalt()));
    user.setCreateTime(LocalDateTime.now());
    this.save(user);
    
    // 5. 初始化用户数据
    initUserData(user.getId());
    
    // 6. 发送欢迎消息
    sendWelcomeMessage(user.getId());
    
    return R.success("注册成功");
}
```

#### 2.1.2 第三方登录业务逻辑
```java
@Override
public R<String> facebookLogin(String accessToken) {
    // 1. 验证Facebook Token
    FacebookUserInfo fbUser = facebookAuthService.getUserInfo(accessToken);
    if (fbUser == null) {
        throw new ServiceException("Facebook登录失败");
    }
    
    // 2. 查找或创建用户
    LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(User::getFbId, fbUser.getId());
    User user = userService.getOne(wrapper);
    
    if (user == null) {
        // 创建新用户
        user = new User();
        user.setFbId(fbUser.getId());
        user.setName(fbUser.getName());
        user.setEmail(fbUser.getEmail());
        user.setPicture(fbUser.getPicture());
        user.setCreatedAt(LocalDateTime.now());
        userService.save(user);
    } else {
        // 更新登录时间
        user.setLastLoginTime(LocalDateTime.now());
        userService.updateById(user);
    }
    
    // 3. 生成Token
    StpUtil.login(user.getId());
    String token = StpUtil.getTokenValue();
    
    return R.data(token);
}
```

### 2.2 聊天功能模块详细设计

#### 2.2.1 聊天室创建业务逻辑
```java
@Override
public ChatRoomDO createChatRoom(ChatRoomDO chatRoomDO) {
    // 1. 参数校验
    if (StringUtils.isBlank(chatRoomDO.getTitle())) {
        chatRoomDO.setTitle("新对话");
    }
    
    // 2. 设置默认值
    chatRoomDO.setUserId(CurrentUserUtil.getUserId());
    chatRoomDO.setCreateTime(new Date());
    chatRoomDO.setUpdateTime(new Date());
    
    // 3. 智能体配置
    if (chatRoomDO.getRoleId() == null) {
        // 使用默认智能体
        IntelligentAgentVO defaultAgent = intelligentAgentService.queryIntelligentByModelName("ZNS");
        chatRoomDO.setRoleId(defaultAgent.getId());
    }
    
    // 4. 保存聊天室
    this.save(chatRoomDO);
    
    // 5. 初始化系统消息
    ChatMessageDO systemMsg = new ChatMessageDO();
    systemMsg.setRoomId(chatRoomDO.getId());
    systemMsg.setContent("你好！我是你的AI助手，有什么可以帮助你的吗？");
    systemMsg.setRole("assistant");
    systemMsg.setCreateTime(new Date());
    chatMessageService.save(systemMsg);
    
    return chatRoomDO;
}
```

#### 2.2.2 AI对话处理业务逻辑
```java
@Override
public ResponseBodyEmitter buildMessageBody(ChatProcessV2Request request) {
    ResponseBodyEmitter emitter = new ResponseBodyEmitter(Long.MAX_VALUE);
    
    // 异步处理对话
    CompletableFuture.runAsync(() -> {
        try {
            // 1. 用户权限校验
            UserBaseInfoVO userInfo = userBaseInfoService.queryUserInfo(CurrentUserUtil.getUserId());
            if (userInfo.getApplyNum() <= 0) {
                throw new BalanceException("积分不足");
            }
            
            // 2. 构建对话上下文
            List<ChatMessageDO> history = chatMessageService.getHistoryMessages(request.getRoomId(), 10);
            List<Message> messages = buildContextMessages(history, request.getPrompt());
            
            // 3. 调用AI接口
            ChatCompletion chatCompletion = ChatCompletion.builder()
                .model(request.getModel())
                .messages(messages)
                .stream(true)
                .build();
            
            // 4. 流式响应处理
            StringBuilder responseContent = new StringBuilder();
            openAiClient.streamChatCompletion(chatCompletion, new EventSourceListener() {
                @Override
                public void onEvent(EventSource eventSource, String id, String type, String data) {
                    if ("[DONE]".equals(data)) {
                        // 对话结束处理
                        finishConversation(request, responseContent.toString(), userInfo);
                        emitter.complete();
                    } else {
                        // 解析并发送数据
                        ChatCompletionResponse response = JSON.parseObject(data, ChatCompletionResponse.class);
                        String content = response.getChoices().get(0).getDelta().getContent();
                        if (content != null) {
                            responseContent.append(content);
                            try {
                                emitter.send(content);
                            } catch (IOException e) {
                                log.error("发送数据失败", e);
                            }
                        }
                    }
                }
                
                @Override
                public void onFailure(EventSource eventSource, Throwable t, Response response) {
                    log.error("AI对话失败", t);
                    emitter.completeWithError(t);
                }
            });
            
        } catch (Exception e) {
            log.error("对话处理异常", e);
            emitter.completeWithError(e);
        }
    });
    
    return emitter;
}

private void finishConversation(ChatProcessV2Request request, String response, UserBaseInfoVO userInfo) {
    // 1. 保存用户消息
    ChatMessageDO userMsg = new ChatMessageDO();
    userMsg.setRoomId(request.getRoomId());
    userMsg.setContent(request.getPrompt());
    userMsg.setRole("user");
    userMsg.setCreateTime(new Date());
    chatMessageService.save(userMsg);
    
    // 2. 保存AI回复
    ChatMessageDO aiMsg = new ChatMessageDO();
    aiMsg.setRoomId(request.getRoomId());
    aiMsg.setContent(response);
    aiMsg.setRole("assistant");
    aiMsg.setCreateTime(new Date());
    chatMessageService.save(aiMsg);
    
    // 3. 扣减积分
    userPointsLogService.deductPoints(userInfo.getId(), getConsumePoints(request.getModel()), "AI对话");
    
    // 4. 更新聊天室
    ChatRoomDO chatRoom = chatRoomService.getById(request.getRoomId());
    chatRoom.setUpdateTime(new Date());
    chatRoomService.updateById(chatRoom);
}
```

### 2.3 支付模块详细设计

#### 2.3.1 微信支付下单业务逻辑
```java
@Override
public WxPayCodeVO generateWxPayQrCode(WxPayOrderDTO dto) {
    // 1. 参数校验
    Product product = productService.getById(dto.getProductId());
    if (product == null) {
        throw new ServiceException("产品不存在");
    }
    
    // 2. 创建订单
    String orderNo = generateOrderNo();
    WxPayOrder order = new WxPayOrder();
    order.setOrderNo(orderNo);
    order.setUserId(CurrentUserUtil.getUserId());
    order.setProductId(dto.getProductId());
    order.setAmount(product.getProductPrice());
    order.setStatus(0); // 待支付
    order.setCreateTime(new Date());
    wxPayOrderService.save(order);
    
    // 3. 调用微信支付API
    WxPayUnifiedOrderRequest request = new WxPayUnifiedOrderRequest();
    request.setBody(product.getProductName());
    request.setOutTradeNo(orderNo);
    request.setTotalFee(product.getProductPrice().multiply(new BigDecimal(100)).intValue());
    request.setTradeType("NATIVE");
    request.setNotifyUrl(wxPayConfig.getNotifyUrl());
    
    try {
        WxPayUnifiedOrderResult result = wxPayService.unifiedOrder(request);
        
        // 4. 返回支付二维码
        WxPayCodeVO vo = new WxPayCodeVO();
        vo.setOrderNo(orderNo);
        vo.setQrCode(result.getCodeURL());
        vo.setAmount(product.getProductPrice());
        vo.setExpireTime(DateUtil.offsetMinute(new Date(), 15));
        
        return vo;
    } catch (WxPayException e) {
        log.error("微信支付下单失败", e);
        throw new ServiceException("支付下单失败");
    }
}
```

#### 2.3.2 支付回调处理业务逻辑
```java
@Override
public void wxPullback(Map<String, Object> body, HttpServletRequest request) {
    try {
        // 1. 验证签名
        String xmlData = request.getParameter("xmlData");
        WxPayOrderNotifyResult result = wxPayService.parseOrderNotifyResult(xmlData);
        
        // 2. 查找订单
        String orderNo = result.getOutTradeNo();
        WxPayOrder order = wxPayOrderService.getByOrderNo(orderNo);
        if (order == null) {
            log.error("订单不存在: {}", orderNo);
            return;
        }
        
        // 3. 防重复处理
        if (order.getStatus() != 0) {
            log.warn("订单已处理: {}", orderNo);
            return;
        }
        
        // 4. 验证金额
        BigDecimal payAmount = new BigDecimal(result.getTotalFee()).divide(new BigDecimal(100));
        if (payAmount.compareTo(order.getAmount()) != 0) {
            log.error("订单金额不匹配: {} vs {}", payAmount, order.getAmount());
            return;
        }
        
        // 5. 更新订单状态
        order.setStatus(1); // 已支付
        order.setPayTime(new Date());
        order.setTransactionId(result.getTransactionId());
        wxPayOrderService.updateById(order);
        
        // 6. 发货处理
        deliverProduct(order);
        
    } catch (Exception e) {
        log.error("支付回调处理失败", e);
    }
}

private void deliverProduct(WxPayOrder order) {
    Product product = productService.getById(order.getProductId());
    
    // 根据产品类型发货
    switch (product.getProductType()) {
        case "POINTS":
            // 充值积分
            userPointsLogService.addPoints(order.getUserId(), product.getNum().intValue(), "购买积分");
            break;
        case "VIP":
            // 开通VIP
            userBaseInfoService.upgradeVip(order.getUserId(), product.getNum().intValue());
            break;
        case "TIMES":
            // 增加使用次数
            userBaseInfoService.addUseTimes(order.getUserId(), product.getNum().intValue());
            break;
        default:
            log.error("未知产品类型: {}", product.getProductType());
    }
    
    // 记录充值日志
    RechargeLog rechargeLog = new RechargeLog();
    rechargeLog.setUserId(order.getUserId());
    rechargeLog.setOrderNo(order.getOrderNo());
    rechargeLog.setAmount(order.getAmount());
    rechargeLog.setChannel("wxpay");
    rechargeLog.setCreateTime(new Date());
    rechargeLogService.save(rechargeLog);
}
```

### 2.4 塔罗牌模块详细设计

#### 2.4.1 随机抽牌业务逻辑
```java
@Override
public List<TarotCardMeaningVO> randomTarot() {
    // 1. 获取所有塔罗牌
    List<TarotCardMeaningVO> allCards = null;
    
    // 2. 防重复抽取(最多重试2次)
    for (int i = 0; i < 2; i++) {
        allCards = tarotCardMeaningService.list()
            .stream()
            .map(TarotCardMeaningConvert.INSTANCE::entityToVO)
            .collect(Collectors.toList());
            
        // 检查名称和图片唯一性
        boolean nameUnique = allCards.stream()
            .map(TarotCardMeaningVO::getName)
            .distinct()
            .count() == allCards.size();
            
        boolean urlUnique = allCards.stream()
            .map(TarotCardMeaningVO::getCardFrontUrl)
            .distinct()
            .count() == allCards.size();
            
        if (nameUnique && urlUnique) {
            break;
        }
        
        if (i == 1) {
            log.error("重复查询两次最终还是有重复");
            throw new ServiceException("抽牌失败，请联系客服");
        }
    }
    
    // 3. 洗牌算法(Fisher-Yates)
    shuffle(allCards);
    
    return allCards;
}

private void shuffle(List<TarotCardMeaningVO> cards) {
    Random rand = new Random();
    
    // Fisher-Yates洗牌算法
    for (int i = cards.size() - 1; i > 0; i--) {
        int j = rand.nextInt(i + 1);
        
        // 交换位置
        TarotCardMeaningVO temp = cards.get(i);
        cards.set(i, cards.get(j));
        cards.set(j, temp);
    }
    
    // 随机设置正逆位
    for (TarotCardMeaningVO card : cards) {
        if (rand.nextBoolean()) {
            card.setPosition("upright");  // 正位
        } else {
            card.setPosition("reversed"); // 逆位
        }
    }
}
```

#### 2.4.2 塔罗解读业务逻辑
```java
@Override
public R buildBatchCompletion(ChatProcessV2Request request) {
    // 1. 用户权限校验
    UserBaseInfoVO userInfo = userBaseInfoService.queryUserInfo(CurrentUserUtil.getUserId());
    
    // 2. 获取牌阵信息
    TarotSpread spread = tarotSpreadService.getById(request.getSpreadId());
    if (spread == null) {
        throw new ServiceException("牌阵不存在");
    }
    
    // 3. 积分校验
    if (userInfo.getApplyNum() < spread.getConsume()) {
        throw new BalanceException("积分不足，需要" + spread.getConsume() + "积分");
    }
    
    // 4. 构建解读提示词
    String prompt = buildTarotPrompt(request.getCards(), spread, request.getQuestion());
    
    // 5. 调用AI解读
    ChatCompletion chatCompletion = ChatCompletion.builder()
        .model("gpt-4")
        .messages(Arrays.asList(
            Message.builder().role("system").content("你是一位专业的塔罗牌解读师...").build(),
            Message.builder().role("user").content(prompt).build()
        ))
        .build();
    
    ChatCompletionResponse response = openAiClient.chatCompletion(chatCompletion);
    String interpretation = response.getChoices().get(0).getMessage().getContent();
    
    // 6. 保存解读记录
    TarotReadingRecord record = new TarotReadingRecord();
    record.setUserId(userInfo.getId());
    record.setSpreadId(spread.getId());
    record.setQuestion(request.getQuestion());
    record.setCards(JSON.toJSONString(request.getCards()));
    record.setInterpretation(interpretation);
    record.setCreateTime(new Date());
    tarotReadingRecordService.save(record);
    
    // 7. 扣减积分
    userPointsLogService.deductPoints(userInfo.getId(), spread.getConsume(), "塔罗解读");
    
    return R.data(interpretation);
}

private String buildTarotPrompt(List<TarotCardMeaningVO> cards, TarotSpread spread, String question) {
    StringBuilder prompt = new StringBuilder();
    prompt.append("牌阵：").append(spread.getName()).append("\n");
    prompt.append("问题：").append(question).append("\n");
    prompt.append("抽到的牌：\n");
    
    for (int i = 0; i < cards.size(); i++) {
        TarotCardMeaningVO card = cards.get(i);
        prompt.append("位置").append(i + 1).append("：")
              .append(card.getName()).append("（")
              .append("upright".equals(card.getPosition()) ? "正位" : "逆位")
              .append("）\n");
    }
    
    prompt.append("\n请根据以上信息进行专业的塔罗牌解读，包括：\n");
    prompt.append("1. 每张牌的含义\n");
    prompt.append("2. 牌与牌之间的关系\n");
    prompt.append("3. 针对问题的具体建议\n");
    prompt.append("4. 总体运势分析\n");
    
    return prompt.toString();
}
```

### 2.5 签到功能模块详细设计

#### 2.5.1 每日签到业务逻辑
```java
@Override
@Transactional
public R<String> checkIn() {
    Integer userId = CurrentUserUtil.getUserId();
    String lockKey = "check_in_lock:" + userId;
    
    // 使用分布式锁防止重复签到
    RLock lock = redissonClient.getLock(lockKey);
    try {
        if (lock.tryLock(10, TimeUnit.SECONDS)) {
            // 1. 检查今日是否已签到
            String today = DateUtil.format(new Date(), "yyyy-MM-dd");
            LambdaQueryWrapper<UserCheckInRecord> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(UserCheckInRecord::getUserId, userId)
                   .eq(UserCheckInRecord::getCheckInDate, today);
            
            if (this.count(wrapper) > 0) {
                return R.fail("今日已签到");
            }
            
            // 2. 计算连续签到天数
            int consecutiveDays = calculateConsecutiveDays(userId);
            
            // 3. 计算签到奖励
            int reward = calculateCheckInReward(consecutiveDays + 1);
            
            // 4. 创建签到记录
            UserCheckInRecord record = new UserCheckInRecord();
            record.setUserId(userId);
            record.setCheckInDate(today);
            record.setConsecutiveDays(consecutiveDays + 1);
            record.setReward(reward);
            record.setCreateTime(new Date());
            this.save(record);
            
            // 5. 发放奖励
            userPointsLogService.addPoints(userId, reward, "每日签到");
            
            // 6. 更新用户签到统计
            updateUserCheckInStats(userId, consecutiveDays + 1);
            
            return R.success("签到成功，获得" + reward + "积分");
            
        } else {
            return R.fail("签到失败，请稍后重试");
        }
    } catch (InterruptedException e) {
        log.error("签到加锁失败", e);
        return R.fail("签到失败");
    } finally {
        if (lock.isHeldByCurrentThread()) {
            lock.unlock();
        }
    }
}

private int calculateConsecutiveDays(Integer userId) {
    // 查询最近的签到记录
    LambdaQueryWrapper<UserCheckInRecord> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(UserCheckInRecord::getUserId, userId)
           .orderByDesc(UserCheckInRecord::getCheckInDate)
           .last("LIMIT 30");
    
    List<UserCheckInRecord> records = this.list(wrapper);
    if (records.isEmpty()) {
        return 0;
    }
    
    // 计算连续天数
    int consecutiveDays = 0;
    Date yesterday = DateUtil.offsetDay(new Date(), -1);
    
    for (UserCheckInRecord record : records) {
        Date checkInDate = DateUtil.parse(record.getCheckInDate(), "yyyy-MM-dd");
        if (DateUtil.isSameDay(checkInDate, yesterday)) {
            consecutiveDays++;
            yesterday = DateUtil.offsetDay(yesterday, -1);
        } else {
            break;
        }
    }
    
    return consecutiveDays;
}

private int calculateCheckInReward(int consecutiveDays) {
    // 基础奖励
    int baseReward = 10;
    
    // 连续签到奖励递增
    if (consecutiveDays <= 7) {
        return baseReward + consecutiveDays;
    } else if (consecutiveDays <= 30) {
        return baseReward + 7 + (consecutiveDays - 7) * 2;
    } else {
        return baseReward + 7 + 23 * 2 + (consecutiveDays - 30) * 3;
    }
}
```

## 3. 异常处理详细设计

### 3.1 全局异常处理
```java
@RestControllerAdvice
public class RestExceptionTranslator {

    @ExceptionHandler(ServiceException.class)
    public R<Void> handleServiceException(ServiceException e) {
        log.error("业务异常", e);
        return R.fail(e.getResultCode(), e.getMessage());
    }

    @ExceptionHandler(BalanceException.class)
    public R<Void> handleBalanceException(BalanceException e) {
        log.error("余额异常", e);
        return R.fail(e.getResultCode(), e.getMessage());
    }

    @ExceptionHandler(AuthException.class)
    public R<Void> handleAuthException(AuthException e) {
        log.error("认证异常", e);
        return R.fail(e.getResultCode(), e.getMessage());
    }

    @ExceptionHandler(Throwable.class)
    public R<Void> handleThrowable(Throwable e) {
        log.error("系统异常", e);
        // 记录异常日志到数据库
        exceptionLogService.saveSystemException(e);
        return R.fail(INTERNAL_SERVER_ERROR, "系统繁忙，请稍后重试");
    }
}
```

### 3.2 业务异常设计
```java
// 业务异常基类
public class ServiceException extends RuntimeException {
    private ResultCode resultCode;

    public ServiceException(String message) {
        super(message);
        this.resultCode = ResultCode.BUSINESS_ERROR;
    }

    public ServiceException(ResultCode resultCode, String message) {
        super(message);
        this.resultCode = resultCode;
    }
}

// 余额不足异常
public class BalanceException extends ServiceException {
    public BalanceException(String message) {
        super(ResultCode.INSUFFICIENT_BALANCE, message);
    }
}

// 认证异常
public class AuthException extends ServiceException {
    public AuthException(String message) {
        super(ResultCode.UN_AUTHORIZED, message);
    }
}
```

## 4. 数据库设计详细说明

### 4.1 核心表结构设计
- **用户表(user_base_info)**: 存储用户基本信息
- **聊天室表(chat_room)**: 存储聊天会话信息
- **聊天消息表(chat_message)**: 存储对话记录
- **智能体表(intelligent_agent)**: 存储AI智能体配置
- **产品表(product)**: 存储可购买的产品信息
- **订单表(orders)**: 存储支付订单信息
- **积分记录表(user_points_log)**: 存储积分变动记录
- **签到记录表(user_check_in_record)**: 存储用户签到记录
- **塔罗牌阵表(tarot_spread)**: 存储塔罗牌阵配置
- **塔罗解读记录表(tarot_reading_record)**: 存储解读历史

### 4.2 索引设计原则
- 主键使用自增ID
- 外键字段建立索引
- 查询频繁的字段建立索引
- 复合查询建立联合索引
- 定期分析慢查询优化索引
