# 超级智能社(SuperAI)测试用例表格

## 1. 用户管理模块测试用例

| 用例编号 | 测试场景 | 前置条件 | 测试步骤 | 预期结果 | 测试数据 | 优先级 |
|---------|---------|---------|---------|---------|---------|--------|
| UM_TC001 | 用户注册-正常流程 | 系统正常运行 | 1.输入有效手机号<br>2.输入验证码<br>3.设置密码<br>4.点击注册 | 注册成功，跳转登录页 | 手机号:13800138000<br>验证码:123456<br>密码:Test123456 | 高 |
| UM_TC002 | 用户注册-手机号已存在 | 手机号已注册 | 1.输入已存在手机号<br>2.输入验证码<br>3.设置密码<br>4.点击注册 | 提示"账号已存在" | 手机号:13800138000 | 高 |
| UM_TC003 | 用户注册-验证码错误 | 系统正常运行 | 1.输入有效手机号<br>2.输入错误验证码<br>3.设置密码<br>4.点击注册 | 提示"验证码错误" | 验证码:000000 | 高 |
| UM_TC004 | 用户注册-密码格式错误 | 系统正常运行 | 1.输入有效手机号<br>2.输入验证码<br>3.输入弱密码<br>4.点击注册 | 提示密码格式要求 | 密码:123 | 中 |
| UM_TC005 | 用户登录-正常流程 | 用户已注册 | 1.输入正确账号<br>2.输入正确密码<br>3.点击登录 | 登录成功，跳转首页 | 账号:13800138000<br>密码:Test123456 | 高 |
| UM_TC006 | 用户登录-密码错误 | 用户已注册 | 1.输入正确账号<br>2.输入错误密码<br>3.点击登录 | 提示"密码错误" | 密码:wrongpass | 高 |
| UM_TC007 | 用户登录-账号不存在 | 系统正常运行 | 1.输入不存在账号<br>2.输入密码<br>3.点击登录 | 提示"账号不存在" | 账号:99999999999 | 高 |
| UM_TC008 | Facebook登录-正常流程 | Facebook账号有效 | 1.点击Facebook登录<br>2.授权登录<br>3.获取用户信息 | 登录成功，创建或更新用户 | Facebook Token | 中 |
| UM_TC009 | Google登录-正常流程 | Google账号有效 | 1.点击Google登录<br>2.授权登录<br>3.获取用户信息 | 登录成功，创建或更新用户 | Google Token | 中 |
| UM_TC010 | 修改个人信息-正常流程 | 用户已登录 | 1.进入个人中心<br>2.修改昵称<br>3.上传头像<br>4.保存 | 修改成功，信息更新 | 昵称:新昵称 | 中 |

## 2. 聊天功能模块测试用例

| 用例编号 | 测试场景 | 前置条件 | 测试步骤 | 预期结果 | 测试数据 | 优先级 |
|---------|---------|---------|---------|---------|---------|--------|
| CM_TC001 | 创建聊天室-正常流程 | 用户已登录 | 1.点击新建对话<br>2.选择智能体<br>3.输入标题<br>4.创建 | 聊天室创建成功 | 标题:测试对话 | 高 |
| CM_TC002 | AI对话-正常流程 | 聊天室已创建，积分充足 | 1.输入问题<br>2.发送消息<br>3.等待AI回复 | AI正常回复，积分扣减 | 问题:你好 | 高 |
| CM_TC003 | AI对话-积分不足 | 聊天室已创建，积分不足 | 1.输入问题<br>2.发送消息 | 提示"积分不足" | 用户积分:0 | 高 |
| CM_TC004 | AI对话-消息过长 | 聊天室已创建 | 1.输入超长消息<br>2.发送消息 | 提示消息长度限制 | 消息:5000字符 | 中 |
| CM_TC005 | 查看聊天历史-正常流程 | 存在历史对话 | 1.进入聊天室<br>2.查看历史消息 | 显示历史对话记录 | - | 中 |
| CM_TC006 | 删除聊天室-正常流程 | 聊天室已创建 | 1.选择聊天室<br>2.点击删除<br>3.确认删除 | 聊天室删除成功 | - | 中 |
| CM_TC007 | 切换AI模型-正常流程 | 用户已登录 | 1.进入设置<br>2.选择AI模型<br>3.保存设置 | 模型切换成功 | 模型:GPT-4 | 中 |
| CM_TC008 | 图片生成-正常流程 | 用户已登录，积分充足 | 1.输入图片描述<br>2.选择画风<br>3.生成图片 | 图片生成成功 | 描述:美丽的风景 | 中 |
| CM_TC009 | 图片生成-积分不足 | 用户已登录，积分不足 | 1.输入图片描述<br>2.点击生成 | 提示"积分不足" | - | 高 |
| CM_TC010 | 文件上传-正常流程 | 用户已登录 | 1.选择文件<br>2.上传文件<br>3.发送消息 | 文件上传成功，AI处理 | 文件:PDF文档 | 低 |

## 3. 智能体模块测试用例

| 用例编号 | 测试场景 | 前置条件 | 测试步骤 | 预期结果 | 测试数据 | 优先级 |
|---------|---------|---------|---------|---------|---------|--------|
| IA_TC001 | 查询智能体列表-正常流程 | 系统有智能体数据 | 1.进入智能体页面<br>2.查看列表 | 显示智能体列表 | - | 高 |
| IA_TC002 | 智能体分类筛选-正常流程 | 系统有分类数据 | 1.选择分类<br>2.查看筛选结果 | 显示对应分类智能体 | 分类:写作助手 | 中 |
| IA_TC003 | 查看智能体详情-正常流程 | 智能体存在 | 1.点击智能体<br>2.查看详情 | 显示详细信息 | 智能体ID:1 | 中 |
| IA_TC004 | 创建自定义智能体-正常流程 | 用户已登录 | 1.点击创建<br>2.填写信息<br>3.保存 | 智能体创建成功 | 名称:我的助手 | 低 |
| IA_TC005 | 使用智能体-正常流程 | 智能体存在 | 1.选择智能体<br>2.开始对话 | 使用次数+1 | - | 中 |

## 4. 塔罗牌模块测试用例

| 用例编号 | 测试场景 | 前置条件 | 测试步骤 | 预期结果 | 测试数据 | 优先级 |
|---------|---------|---------|---------|---------|---------|--------|
| TR_TC001 | 查询牌阵列表-正常流程 | 系统有牌阵数据 | 1.进入塔罗页面<br>2.查看牌阵列表 | 显示可用牌阵 | - | 高 |
| TR_TC002 | 随机抽牌-正常流程 | 牌阵已选择 | 1.选择牌阵<br>2.点击抽牌<br>3.查看结果 | 随机生成塔罗牌 | 牌阵:三牌阵 | 高 |
| TR_TC003 | 塔罗解读-正常流程 | 已抽牌，积分充足 | 1.输入问题<br>2.点击解读<br>3.等待结果 | AI解读成功，积分扣减 | 问题:感情运势 | 高 |
| TR_TC004 | 塔罗解读-积分不足 | 已抽牌，积分不足 | 1.输入问题<br>2.点击解读 | 提示"积分不足" | - | 高 |
| TR_TC005 | 查看解读历史-正常流程 | 存在解读记录 | 1.进入历史页面<br>2.查看记录 | 显示历史解读 | - | 中 |
| TR_TC006 | 每日洞察-正常流程 | 系统正常运行 | 1.进入洞察页面<br>2.查看今日洞察 | 显示每日洞察内容 | - | 中 |
| TR_TC007 | 分享解读结果-正常流程 | 解读已完成 | 1.点击分享<br>2.选择分享方式 | 分享成功 | - | 低 |

## 5. 支付模块测试用例

| 用例编号 | 测试场景 | 前置条件 | 测试步骤 | 预期结果 | 测试数据 | 优先级 |
|---------|---------|---------|---------|---------|---------|--------|
| PM_TC001 | 查询产品列表-正常流程 | 系统有产品数据 | 1.进入商城页面<br>2.查看产品列表 | 显示可购买产品 | - | 高 |
| PM_TC002 | 微信支付下单-正常流程 | 用户已登录，产品存在 | 1.选择产品<br>2.点击购买<br>3.选择微信支付<br>4.确认支付 | 生成支付二维码 | 产品ID:1 | 高 |
| PM_TC003 | 支付宝支付下单-正常流程 | 用户已登录，产品存在 | 1.选择产品<br>2.点击购买<br>3.选择支付宝<br>4.确认支付 | 生成支付链接 | 产品ID:1 | 高 |
| PM_TC004 | 支付成功回调-正常流程 | 订单已创建 | 1.模拟支付成功<br>2.接收回调通知<br>3.处理订单 | 订单状态更新，发货成功 | 订单号:test001 | 高 |
| PM_TC005 | 支付失败回调-正常流程 | 订单已创建 | 1.模拟支付失败<br>2.接收回调通知 | 订单状态更新为失败 | - | 中 |
| PM_TC006 | 查询订单状态-正常流程 | 订单存在 | 1.输入订单号<br>2.查询状态 | 显示订单状态 | 订单号:test001 | 中 |
| PM_TC007 | 查询充值记录-正常流程 | 存在充值记录 | 1.进入充值记录页面<br>2.查看记录 | 显示充值历史 | - | 中 |
| PM_TC008 | 申请提现-正常流程 | 用户有余额 | 1.进入提现页面<br>2.输入金额<br>3.提交申请 | 提现申请成功 | 金额:100 | 中 |

## 6. 积分系统模块测试用例

| 用例编号 | 测试场景 | 前置条件 | 测试步骤 | 预期结果 | 测试数据 | 优先级 |
|---------|---------|---------|---------|---------|---------|--------|
| PS_TC001 | 查询积分余额-正常流程 | 用户已登录 | 1.进入个人中心<br>2.查看积分余额 | 显示当前积分 | - | 高 |
| PS_TC002 | 积分消耗-正常流程 | 用户有积分 | 1.使用AI功能<br>2.消耗积分 | 积分正确扣减 | 消耗:10积分 | 高 |
| PS_TC003 | 积分充值-正常流程 | 支付成功 | 1.购买积分产品<br>2.支付成功 | 积分正确增加 | 充值:100积分 | 高 |
| PS_TC004 | 查询积分记录-正常流程 | 存在积分记录 | 1.进入积分记录页面<br>2.查看记录 | 显示积分变动记录 | - | 中 |
| PS_TC005 | 积分不足提示-正常流程 | 用户积分不足 | 1.使用需要积分的功能 | 提示"积分不足" | 用户积分:0 | 高 |
| PS_TC006 | 积分过期处理-正常流程 | 有过期积分 | 1.系统定时任务执行<br>2.清理过期积分 | 过期积分被清理 | - | 低 |

## 7. 签到模块测试用例

| 用例编号 | 测试场景 | 前置条件 | 测试步骤 | 预期结果 | 测试数据 | 优先级 |
|---------|---------|---------|---------|---------|---------|--------|
| SI_TC001 | 每日签到-正常流程 | 用户已登录，今日未签到 | 1.进入签到页面<br>2.点击签到<br>3.获得奖励 | 签到成功，获得积分 | 奖励:10积分 | 高 |
| SI_TC002 | 重复签到-异常流程 | 用户今日已签到 | 1.进入签到页面<br>2.点击签到 | 提示"今日已签到" | - | 高 |
| SI_TC003 | 连续签到-正常流程 | 用户连续多日签到 | 1.连续签到<br>2.查看奖励 | 奖励递增 | 连续3天 | 中 |
| SI_TC004 | 断签重置-正常流程 | 用户断签后重新签到 | 1.断签一天<br>2.重新签到 | 连续天数重置为1 | - | 中 |
| SI_TC005 | 查看签到记录-正常流程 | 存在签到记录 | 1.进入签到记录页面<br>2.查看历史 | 显示签到日历 | - | 低 |
| SI_TC006 | 并发签到-异常流程 | 多个请求同时签到 | 1.同时发起多个签到请求 | 只有一个成功，其他失败 | - | 中 |

## 8. 系统管理模块测试用例

| 用例编号 | 测试场景 | 前置条件 | 测试步骤 | 预期结果 | 测试数据 | 优先级 |
|---------|---------|---------|---------|---------|---------|--------|
| SM_TC001 | 操作日志记录-正常流程 | 用户进行操作 | 1.执行任意操作<br>2.查看日志 | 操作被正确记录 | - | 中 |
| SM_TC002 | 异常日志记录-正常流程 | 系统发生异常 | 1.触发系统异常<br>2.查看异常日志 | 异常被正确记录 | - | 中 |
| SM_TC003 | 定时任务执行-正常流程 | 定时任务配置正确 | 1.等待定时任务执行<br>2.查看执行结果 | 任务正常执行 | - | 低 |
| SM_TC004 | 系统配置查询-正常流程 | 配置数据存在 | 1.查询系统配置<br>2.获取配置值 | 返回正确配置 | - | 中 |
| SM_TC005 | 健康检查-正常流程 | 系统正常运行 | 1.访问健康检查接口<br>2.查看状态 | 返回系统健康状态 | - | 低 |

## 测试覆盖率统计

| 模块 | 测试用例数 | 高优先级 | 中优先级 | 低优先级 | 覆盖率 |
|------|-----------|---------|---------|---------|--------|
| 用户管理 | 10 | 7 | 3 | 0 | 95% |
| 聊天功能 | 10 | 4 | 5 | 1 | 90% |
| 智能体 | 5 | 1 | 3 | 1 | 85% |
| 塔罗牌 | 7 | 3 | 3 | 1 | 90% |
| 支付模块 | 8 | 4 | 4 | 0 | 95% |
| 积分系统 | 6 | 4 | 1 | 1 | 90% |
| 签到模块 | 6 | 2 | 3 | 1 | 85% |
| 系统管理 | 5 | 0 | 3 | 2 | 80% |
| **总计** | **57** | **25** | **25** | **7** | **90%** |
