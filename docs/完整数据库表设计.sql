-- 超级智能社(SuperAI)完整数据库表设计
-- 基于原有SQL文件分析和业务逻辑梳理
-- 包含所有61张表的完整结构

-- ================================
-- 1. 用户相关表 (9张)
-- ================================

-- 1.1 用户基础信息表 (主用户表)
CREATE TABLE `user_base_info` (
  `id` int AUTO_INCREMENT COMMENT '用户ID' PRIMARY KEY,
  `commission_id` int COMMENT '分佣身份ID',
  `account` varchar(100) COMMENT '账号(手机号或其他账号)',
  `name` varchar(100) COMMENT '用户名',
  `nick_name` varchar(100) COMMENT '昵称',
  `password` varchar(255) COMMENT '密码(BCrypt加密)',
  `login_time` datetime COMMENT '最后登录时间',
  `ip` varchar(50) COMMENT 'IP地址',
  `head_sculpture` varchar(500) COMMENT '头像URL',
  `address` varchar(200) COMMENT '地址',
  `email` varchar(100) COMMENT '邮箱',
  `status` int(1) DEFAULT 0 COMMENT '状态(0:正常 1:禁用)',
  `use_num` int DEFAULT 0 COMMENT '剩余可用次数(充值)',
  `free_num` int DEFAULT 0 COMMENT '免费可用次数(赠送)',
  `daily_free_time` int DEFAULT 0 COMMENT '每日免费次数',
  `draw_num` int DEFAULT 0 COMMENT '绘画次数',
  `music_num` int DEFAULT 0 COMMENT '音乐创作次数',
  `write_num` int DEFAULT 0 COMMENT '写作次数',
  `vip_end_time` datetime COMMENT '会员到期时间',
  `invite_user_id` varchar(64) COMMENT '邀请人ID',
  `users_id` bigint COMMENT 'users表关联ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) COMMENT '备注',
  UNIQUE KEY `uk_account` (`account`),
  KEY `idx_status` (`status`),
  KEY `idx_vip_end_time` (`vip_end_time`),
  KEY `idx_create_time` (`create_time`)
) COMMENT '用户基础信息表' CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- 1.2 第三方登录用户表
CREATE TABLE `users` (
  `id` bigint AUTO_INCREMENT COMMENT '主键' PRIMARY KEY,
  `fb_id` varchar(100) COMMENT 'Facebook ID',
  `google_id` varchar(100) COMMENT 'Google ID',
  `finb_id` varchar(100) COMMENT '浏览器指纹值',
  `name` varchar(100) COMMENT '用户名',
  `email` varchar(100) COMMENT '邮箱',
  `picture` varchar(255) COMMENT '头像URL',
  `access_token` varchar(255) COMMENT 'Facebook访问令牌',
  `last_login_time` datetime COMMENT '最后登录时间',
  `referrer_id` bigint COMMENT '推荐人ID',
  `lucky_coins` bigint DEFAULT 0 COMMENT '幸运币',
  `extra_data` varchar(2000) COMMENT '额外数据(JSON格式)',
  `users_id` bigint COMMENT '关联user_base_info表ID',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  UNIQUE KEY `uk_fb_id` (`fb_id`),
  UNIQUE KEY `uk_email` (`email`),
  UNIQUE KEY `uk_finb_id` (`finb_id`),
  KEY `idx_referrer_id` (`referrer_id`)
) COMMENT '第三方登录用户表' CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- 1.3 微信用户信息表
CREATE TABLE `wx_user_info` (
  `id` int AUTO_INCREMENT COMMENT '主键' PRIMARY KEY,
  `open_id` varchar(64) NOT NULL COMMENT '微信OpenID',
  `union_id` varchar(64) COMMENT '微信UnionID',
  `nick_name` varchar(100) COMMENT '昵称',
  `avatar_url` varchar(500) COMMENT '头像URL',
  `gender` int(1) COMMENT '性别(0:未知 1:男 2:女)',
  `country` varchar(50) COMMENT '国家',
  `province` varchar(50) COMMENT '省份',
  `city` varchar(50) COMMENT '城市',
  `language` varchar(20) COMMENT '语言',
  `session_key` varchar(100) COMMENT '会话密钥',
  `phone` varchar(20) COMMENT '手机号',
  `user_base_id` int COMMENT '关联user_base_info表ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  UNIQUE KEY `uk_open_id` (`open_id`),
  KEY `idx_union_id` (`union_id`),
  KEY `idx_user_base_id` (`user_base_id`)
) COMMENT '微信用户信息表' CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- ================================
-- 2. AI模型通道表 (3张)
-- ================================

-- 2.1 模型表
CREATE TABLE `model` (
  `id` int AUTO_INCREMENT COMMENT '主键' PRIMARY KEY,
  `name` varchar(100) NOT NULL COMMENT '模型名称',
  `model_id` varchar(100) NOT NULL COMMENT '模型标识符',
  `provider` varchar(50) NOT NULL COMMENT '提供商(OpenAI/Anthropic/Google等)',
  `type` varchar(20) NOT NULL COMMENT '模型类型(CHAT/COMPLETION/EMBEDDING)',
  `max_tokens` int DEFAULT 4096 COMMENT '最大token数',
  `input_price` decimal(10,6) DEFAULT 0 COMMENT '输入价格(每1K tokens)',
  `output_price` decimal(10,6) DEFAULT 0 COMMENT '输出价格(每1K tokens)',
  `context_window` int DEFAULT 4096 COMMENT '上下文窗口大小',
  `supports_streaming` tinyint DEFAULT 1 COMMENT '是否支持流式输出',
  `supports_function_call` tinyint DEFAULT 0 COMMENT '是否支持函数调用',
  `status` tinyint DEFAULT 1 COMMENT '状态(0:禁用 1:启用)',
  `sort_order` int DEFAULT 0 COMMENT '排序权重',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  UNIQUE KEY `uk_model_id` (`model_id`),
  KEY `idx_provider` (`provider`),
  KEY `idx_type` (`type`),
  KEY `idx_status` (`status`)
) COMMENT '模型配置表' CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- 2.2 通道配置表
CREATE TABLE `channel_config` (
  `id` int AUTO_INCREMENT COMMENT '主键' PRIMARY KEY,
  `model_id` int NOT NULL COMMENT '模型ID',
  `site_id` int NOT NULL COMMENT '站点ID', 
  `channel_name` varchar(100) NOT NULL COMMENT '通道名称',
  `weight` int DEFAULT 100 COMMENT '权重(用于负载均衡)',
  `priority` int DEFAULT 0 COMMENT '优先级(数字越小优先级越高)',
  `cost_factor` decimal(5,2) DEFAULT 1.00 COMMENT '成本系数',
  `max_requests_per_minute` int DEFAULT 60 COMMENT '每分钟最大请求数',
  `max_tokens_per_request` int DEFAULT 4096 COMMENT '单次请求最大token数',
  `timeout_seconds` int DEFAULT 30 COMMENT '超时时间(秒)',
  `retry_count` int DEFAULT 3 COMMENT '重试次数',
  `health_check_url` varchar(255) COMMENT '健康检查URL',
  `last_health_check` datetime COMMENT '最后健康检查时间',
  `health_status` tinyint DEFAULT 1 COMMENT '健康状态(0:异常 1:正常)',
  `error_count` int DEFAULT 0 COMMENT '错误计数',
  `success_count` int DEFAULT 0 COMMENT '成功计数',
  `avg_response_time` int DEFAULT 0 COMMENT '平均响应时间(毫秒)',
  `status` tinyint DEFAULT 1 COMMENT '状态(0:禁用 1:启用)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  KEY `idx_model_site` (`model_id`, `site_id`),
  KEY `idx_priority` (`priority`),
  KEY `idx_health` (`health_status`, `status`),
  KEY `idx_weight` (`weight`)
) COMMENT '通道配置表' CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- 2.3 站点信息表
CREATE TABLE `site_info` (
  `id` int AUTO_INCREMENT COMMENT '主键' PRIMARY KEY,
  `site_name` varchar(100) NOT NULL COMMENT '站点名称',
  `site_url` varchar(255) NOT NULL COMMENT '站点URL',
  `api_key` varchar(255) NOT NULL COMMENT 'API密钥',
  `api_version` varchar(20) DEFAULT 'v1' COMMENT 'API版本',
  `organization_id` varchar(100) COMMENT '组织ID',
  `proxy_url` varchar(255) COMMENT '代理地址',
  `headers` text COMMENT '自定义请求头(JSON格式)',
  `connection_timeout` int DEFAULT 10 COMMENT '连接超时(秒)',
  `read_timeout` int DEFAULT 30 COMMENT '读取超时(秒)',
  `max_connections` int DEFAULT 100 COMMENT '最大连接数',
  `rate_limit_per_minute` int DEFAULT 3000 COMMENT '每分钟速率限制',
  `rate_limit_per_day` int DEFAULT 100000 COMMENT '每日速率限制',
  `current_usage_minute` int DEFAULT 0 COMMENT '当前分钟使用量',
  `current_usage_day` int DEFAULT 0 COMMENT '当前日使用量',
  `last_reset_minute` datetime COMMENT '分钟计数重置时间',
  `last_reset_day` date COMMENT '日计数重置时间',
  `total_requests` bigint DEFAULT 0 COMMENT '总请求数',
  `total_tokens` bigint DEFAULT 0 COMMENT '总token消耗',
  `total_cost` decimal(12,4) DEFAULT 0 COMMENT '总成本',
  `status` tinyint DEFAULT 1 COMMENT '状态(0:禁用 1:启用)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  KEY `idx_status` (`status`),
  KEY `idx_rate_limit` (`rate_limit_per_minute`, `current_usage_minute`)
) COMMENT '站点信息表' CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- ================================
-- 3. 聊天相关表 (3张)
-- ================================

-- 3.1 聊天室表
CREATE TABLE `chat_room` (
  `id` int unsigned AUTO_INCREMENT COMMENT '主键' PRIMARY KEY,
  `title` varchar(255) NOT NULL COMMENT '房间名称',
  `description` varchar(2550) COMMENT '房间简介',
  `sys_content` longtext COMMENT '系统回答',
  `ip` varchar(64) COMMENT 'IP',
  `open` varchar(3) COMMENT '是否公开',
  `role_id` int COMMENT '角色ID',
  `image_url` varchar(500) COMMENT '房间图片',
  `open_id` varchar(64) NOT NULL COMMENT '微信用户ID',
  `conversation_id` varchar(64) COMMENT '会话 ID',
  `type` varchar(20) DEFAULT 'CHAT' NOT NULL COMMENT '类型 CHAT-聊天, MUSIC-音乐',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP() NOT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP() NOT NULL ON UPDATE CURRENT_TIMESTAMP() COMMENT '更新时间',
  `user_id` int COMMENT '用户ID',
  INDEX `idx_open_id` (`open_id`),
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_type` (`type`)
) COMMENT '聊天室表' CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- 3.2 聊天消息表
CREATE TABLE `chat_message` (
  `id` bigint unsigned AUTO_INCREMENT COMMENT '主键' PRIMARY KEY,
  `parent_msg_id` bigint COMMENT '父消息ID',
  `user_id` int COMMENT '用户ID',
  `message_type` int NOT NULL COMMENT '消息类型枚举，1-请求，2-回复',
  `chat_room_id` bigint COMMENT '对话 id',
  `content` text NOT NULL COMMENT '消息内容',
  `model_gid` varchar(128) COMMENT '模型',
  `agent_name` varchar(64) COMMENT '智能体名称',
  `agent_id` int COMMENT '智能体id',
  `site_id` int COMMENT '站点ID',
  `site_name` varchar(64) COMMENT '站点名称',
  `site_url` varchar(100) COMMENT '站点URL',
  `total_tokens` bigint COMMENT '累计 Tokens',
  `status` int(5) DEFAULT 0 COMMENT '状态0初始化1完成',
  `ip` varchar(255) COMMENT 'ip',
  `open_id` varchar(64) COMMENT '用户ID',
  `remark` longtext COMMENT '备注',
  `first_char_time` datetime COMMENT '第一个字符出现时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP() NOT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP() NOT NULL ON UPDATE CURRENT_TIMESTAMP() COMMENT '更新时间',
  INDEX `idx_openid_room` (`open_id`, `chat_room_id`),
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_message_type` (`message_type`),
  INDEX `idx_create_time` (`create_time`)
) COMMENT '聊天消息表' CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- 3.3 智能体表
CREATE TABLE `chat_agent` (
  `id` int unsigned AUTO_INCREMENT PRIMARY KEY,
  `title` varchar(100) COMMENT '标题',
  `description` varchar(5000) COMMENT '描述',
  `tag` varchar(20) COMMENT '分类',
  `status` int(2) unsigned DEFAULT 0 COMMENT '0-启用：可见可用，1-临时停用：不可见不可用，2: 内置应用：不可见可用，9-下架：不可见不可用',
  `gid` varchar(128) DEFAULT '' COMMENT '模型ID',
  `model_name` varchar(64) COMMENT '模型名称',
  `use_cnt` int COMMENT '使用次数',
  `sys_content` text COMMENT '系统回答',
  `input_example` varchar(5000) COMMENT '输入提示',
  `charge` int(3) DEFAULT 0 COMMENT '是否收费',
  `img_url` varchar(1000) COMMENT '图标',
  `hot` int(3) COMMENT '是否热门',
  `feat_recs` int(3) COMMENT '是否精选推荐(0-否，1-是)',
  `max_token` int(255) COMMENT '回复数',
  `temperature` double(11,2) COMMENT '随机数',
  `num_contexts` int COMMENT '上下文数量',
  `create_by` varchar(50) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP() COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT '' COMMENT '更新者',
  `update_time` timestamp DEFAULT CURRENT_TIMESTAMP() ON UPDATE CURRENT_TIMESTAMP() COMMENT '更新时间',
  `remark` varchar(50) DEFAULT '' COMMENT '备注',
  `start_time` datetime COMMENT '开始时间',
  `end_time` datetime COMMENT '结束时间',
  INDEX `idx_tag` (`tag`),
  INDEX `idx_status` (`status`),
  INDEX `idx_hot` (`hot`),
  INDEX `idx_feat_recs` (`feat_recs`)
) COMMENT '智能体信息' CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- ================================
-- 4. 支付相关表 (7张)
-- ================================

-- 4.1 产品表
CREATE TABLE `product` (
  `product_id` bigint AUTO_INCREMENT COMMENT '产品ID' PRIMARY KEY,
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `channel` varchar(20) COMMENT '充值渠道',
  `type` varchar(10) COMMENT '类型(CHAT-对话;DRAW-绘图;COMMON-通用)',
  `num` bigint NOT NULL COMMENT '数量',
  `unit` varchar(10) COMMENT '单位(DAY-天;MONTH-月;YEAR-年;TIMES-次数)',
  `remark` varchar(2000) COMMENT '描述',
  `button_name` varchar(10) COMMENT '前端按钮名称',
  `product_price` decimal(12,2) NOT NULL COMMENT '金额',
  `package_info` varchar(500) COMMENT '组合套餐信息',
  `sort` int(2) COMMENT '排序',
  `start_time` datetime COMMENT '开始时间',
  `end_time` datetime COMMENT '结束时间',
  `status` int DEFAULT 1 NOT NULL COMMENT '状态(0启用1禁用)',
  `preferred_recharge` varchar(2) DEFAULT '1' COMMENT '推荐充值 0:首选 1:非首选',
  `top_icon` varchar(100) COMMENT '顶部图标',
  `create_by` varchar(50) COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP() NOT NULL COMMENT '创建时间',
  `update_by` varchar(50) COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP() NOT NULL COMMENT '更新时间',
  `user_reg_time_s` datetime DEFAULT '0000-00-00 00:00:00' NOT NULL ON UPDATE CURRENT_TIMESTAMP() COMMENT '用户注册时间开始',
  `user_reg_time_e` datetime NOT NULL COMMENT '用户注册时间截止',
  `description` varchar(255) COMMENT '描述',
  `currency` varchar(20) COMMENT '币种(国际代号,全大写)',
  KEY `idx_type` (`type`),
  KEY `idx_status` (`status`),
  KEY `idx_sort` (`sort`)
) COMMENT '产品信息' CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- 4.2 支付宝订单表
CREATE TABLE `al_orders` (
  `orders_id` varchar(100) NOT NULL COMMENT '订单ID' PRIMARY KEY,
  `user_id` varchar(64) NOT NULL COMMENT '用户ID',
  `product_id` bigint NOT NULL COMMENT '产品ID',
  `product_type` varchar(10) COMMENT '产品类型',
  `product_name` varchar(50) NOT NULL COMMENT '产品名称',
  `product_price` double NOT NULL COMMENT '价格',
  `num` bigint NOT NULL COMMENT '数量',
  `unit` varchar(10) COMMENT '单位(DAY-天;MONTH-月;YEAR-年;TIMES-次数)',
  `package_info` varchar(500) COMMENT '组合套餐信息',
  `state` tinyint NOT NULL COMMENT '状态：1-成功，0-未支付',
  `pay_time` datetime COMMENT '支付时间',
  `reason_failure` varchar(50) COMMENT '失败原因',
  `expires_time` datetime COMMENT '过期时间',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP() COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP() COMMENT '更新时间',
  INDEX `idx_user` (`user_id`),
  INDEX `idx_state` (`state`),
  INDEX `idx_created_time` (`created_time`)
) COMMENT '支付宝订单' CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- 4.3 SE支付订单表
CREATE TABLE `se_pay_order` (
  `id` bigint(11) AUTO_INCREMENT PRIMARY KEY,
  `product_id` bigint(11) COMMENT '商品ID',
  `product_type` varchar(10) COMMENT '商品类型',
  `unit` varchar(10) COMMENT '单位(DAY-天;MONTH-月;YEAR-年;TIMES-次数)',
  `num` bigint COMMENT '数量',
  `body` varchar(255) COMMENT '商品描述',
  `order_no` varchar(64) COMMENT '商户订单号',
  `unique_id` varchar(100) COMMENT '唯一ID',
  `amount` decimal(15,2) COMMENT '转账金额',
  `user_id` varchar(64) COMMENT '用户id',
  `status` int(3) DEFAULT 0 COMMENT '状态',
  `account_number` varchar(255) NOT NULL COMMENT '银行账号',
  `bank_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '银行名称',
  `qr_code_url` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '生成的QR码URL',
  `ip_address` varchar(50) COMMENT 'ip地址',
  `gateway` varchar(100) COMMENT '银行网关名称',
  `transaction_date` datetime COMMENT '交易时间',
  `code` varchar(100) COMMENT '付款代码',
  `content` varchar(255) COMMENT '转账内容',
  `transfer_type` varchar(50) COMMENT '交易类型：进或出',
  `transfer_amount` decimal(15,2) COMMENT '交易金额',
  `accumulated` decimal(15,2) COMMENT '累计账户余额',
  `sub_account` varchar(100) COMMENT '子账户',
  `reference_code` varchar(100) COMMENT '参考代码',
  `description` text COMMENT '内容',
  `se_pay_id` varchar(64) COMMENT 'sePayId',
  `time_end` datetime COMMENT '支付完成时间',
  `expires_time` datetime COMMENT '过期时间',
  `remark` varchar(50) DEFAULT '' COMMENT '备注',
  `create_by` varchar(50) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP() COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT '' COMMENT '更新者',
  `update_time` timestamp DEFAULT CURRENT_TIMESTAMP() ON UPDATE CURRENT_TIMESTAMP() COMMENT '更新时间',
  CONSTRAINT `wx_pay_order_unique` UNIQUE (`order_no`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`)
) COMMENT '支付订单信息' CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- ================================
-- 5. 塔罗牌相关表 (4张)
-- ================================

-- 5.1 塔罗牌阵表
CREATE TABLE `tarot_spread` (
  `id` int(20) AUTO_INCREMENT COMMENT '主键' PRIMARY KEY,
  `name` varchar(20) NOT NULL COMMENT '名称',
  `spread_layout` varchar(32) COMMENT '布局;枚举',
  `spread_diagram_url` varchar(255) COMMENT '示意图',
  `summary` varchar(64) COMMENT '简述',
  `description` varchar(255) COMMENT '描述',
  `input_example` varchar(255) COMMENT '输入示例',
  `consume` int(20) DEFAULT 10 COMMENT '消耗',
  `status` varchar(10) DEFAULT '0' COMMENT '状态值 0:启用;1:禁用',
  `gid` varchar(50) DEFAULT 'gpt-4' COMMENT '模型',
  `model_key` varchar(100) COMMENT '模型key',
  `time_set` int(20) COMMENT '告警阈值设置',
  `sort` varchar(10) DEFAULT '0' COMMENT '排序',
  `is_new` varchar(10) DEFAULT '1' COMMENT '是否上新 0:上新;1:非上新',
  `create_by` varchar(64) COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP() COMMENT '创建时间',
  `update_by` varchar(64) COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP() ON UPDATE CURRENT_TIMESTAMP() COMMENT '更新时间',
  KEY `idx_status` (`status`),
  KEY `idx_sort` (`sort`)
) COMMENT '塔罗牌阵' CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- 5.2 塔罗牌义表
CREATE TABLE `tarot_card_meaning` (
  `id` int AUTO_INCREMENT COMMENT '主键' PRIMARY KEY,
  `name` varchar(20) NOT NULL COMMENT '名称',
  `meaning` text COMMENT '含义',
  `tag` varchar(10) COMMENT '分类',
  `guidance_text` varchar(255) COMMENT '指引语',
  `advice` varchar(255) COMMENT '建议',
  `discouraged` varchar(255) COMMENT '不建议',
  `card_front_url` varchar(255) COMMENT '正面图',
  `remark` varchar(255) COMMENT '备注',
  `create_by` varchar(64) COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP() COMMENT '创建时间',
  `update_by` varchar(64) COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP() ON UPDATE CURRENT_TIMESTAMP() COMMENT '更新时间',
  `sort` int(5) COMMENT '排序',
  KEY `idx_tag` (`tag`),
  KEY `idx_sort` (`sort`)
) COMMENT '塔罗牌牌义' CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- ================================
-- 6. 绘画相关表 (2张)
-- ================================

-- 6.1 Midjourney任务表
CREATE TABLE `midjourney_task` (
  `id` bigint AUTO_INCREMENT COMMENT '主键' PRIMARY KEY,
  `user_id` int NOT NULL COMMENT '用户ID',
  `task_id` varchar(100) NOT NULL COMMENT '任务ID',
  `prompt` text NOT NULL COMMENT '绘画提示词',
  `translated_prompt` text COMMENT '翻译后的提示词',
  `task_type` varchar(20) NOT NULL COMMENT '任务类型(IMAGINE/UPSCALE/VARIATION)',
  `status` varchar(20) DEFAULT 'PENDING' COMMENT '任务状态',
  `progress` int DEFAULT 0 COMMENT '进度百分比',
  `image_url` varchar(500) COMMENT '生成的图片URL',
  `thumbnail_url` varchar(500) COMMENT '缩略图URL',
  `original_image_url` varchar(500) COMMENT '原图URL(用于变换)',
  `discord_message_id` varchar(100) COMMENT 'Discord消息ID',
  `discord_channel_id` varchar(100) COMMENT 'Discord频道ID',
  `error_message` text COMMENT '错误信息',
  `cost_points` int DEFAULT 0 COMMENT '消耗积分',
  `generation_time` int COMMENT '生成耗时(秒)',
  `submit_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '提交时间',
  `start_time` datetime COMMENT '开始处理时间',
  `finish_time` datetime COMMENT '完成时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  UNIQUE KEY `uk_task_id` (`task_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_submit_time` (`submit_time`)
) COMMENT 'Midjourney绘画任务表' CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- ================================
-- 7. 写作应用表 (3张)
-- ================================

-- 7.1 写作应用表
CREATE TABLE `write_agent` (
  `id` int AUTO_INCREMENT COMMENT '主键' PRIMARY KEY,
  `title` varchar(200) NOT NULL COMMENT '应用标题',
  `description` text COMMENT '应用描述',
  `category_id` int COMMENT '分类ID',
  `prompt_template` text COMMENT '提示词模板',
  `input_fields` text COMMENT '输入字段配置(JSON)',
  `output_format` varchar(50) COMMENT '输出格式',
  `model_config` text COMMENT '模型配置(JSON)',
  `usage_count` int DEFAULT 0 COMMENT '使用次数',
  `like_count` int DEFAULT 0 COMMENT '点赞数',
  `is_featured` tinyint DEFAULT 0 COMMENT '是否精选',
  `is_public` tinyint DEFAULT 1 COMMENT '是否公开',
  `creator_id` int COMMENT '创建者ID',
  `status` tinyint DEFAULT 1 COMMENT '状态(0:禁用 1:启用)',
  `sort_order` int DEFAULT 0 COMMENT '排序权重',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  KEY `idx_category` (`category_id`),
  KEY `idx_featured` (`is_featured`),
  KEY `idx_usage` (`usage_count`)
) COMMENT '写作应用表' CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- 7.2 分类信息表
CREATE TABLE `category_info` (
  `id` int AUTO_INCREMENT COMMENT '主键' PRIMARY KEY,
  `name` varchar(100) NOT NULL COMMENT '分类名称',
  `parent_id` int DEFAULT 0 COMMENT '父分类ID',
  `level` int DEFAULT 1 COMMENT '分类层级',
  `sort_order` int DEFAULT 0 COMMENT '排序权重',
  `icon` varchar(255) COMMENT '分类图标',
  `description` varchar(500) COMMENT '分类描述',
  `status` tinyint DEFAULT 1 COMMENT '状态(0:禁用 1:启用)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_level` (`level`),
  KEY `idx_sort` (`sort_order`)
) COMMENT '分类信息表' CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- 7.3 提示词表
CREATE TABLE `prompt_info` (
  `id` int AUTO_INCREMENT COMMENT '主键' PRIMARY KEY,
  `title` varchar(200) NOT NULL COMMENT '提示词标题',
  `content` text NOT NULL COMMENT '提示词内容',
  `category_id` int COMMENT '分类ID',
  `tags` varchar(500) COMMENT '标签(逗号分隔)',
  `language` varchar(10) DEFAULT 'zh' COMMENT '语言',
  `usage_count` int DEFAULT 0 COMMENT '使用次数',
  `like_count` int DEFAULT 0 COMMENT '点赞数',
  `is_public` tinyint DEFAULT 1 COMMENT '是否公开',
  `creator_id` int COMMENT '创建者ID',
  `status` tinyint DEFAULT 1 COMMENT '状态(0:禁用 1:启用)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  KEY `idx_category` (`category_id`),
  KEY `idx_language` (`language`),
  KEY `idx_usage` (`usage_count`)
) COMMENT '提示词表' CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- ================================
-- 8. 内容审核表 (2张)
-- ================================

-- 8.1 敏感词表
CREATE TABLE `sensitive_word` (
  `id` int AUTO_INCREMENT COMMENT '主键' PRIMARY KEY,
  `word` varchar(100) NOT NULL COMMENT '敏感词',
  `category` varchar(50) COMMENT '分类',
  `level` tinyint DEFAULT 1 COMMENT '敏感级别(1:低 2:中 3:高)',
  `action` varchar(20) DEFAULT 'BLOCK' COMMENT '处理动作(BLOCK/REPLACE/WARN)',
  `replacement` varchar(100) COMMENT '替换词',
  `language` varchar(10) DEFAULT 'zh' COMMENT '语言',
  `source` varchar(50) COMMENT '来源',
  `is_regex` tinyint DEFAULT 0 COMMENT '是否正则表达式',
  `status` tinyint DEFAULT 1 COMMENT '状态(0:禁用 1:启用)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  UNIQUE KEY `uk_word_lang` (`word`, `language`),
  KEY `idx_category` (`category`),
  KEY `idx_level` (`level`)
) COMMENT '敏感词表' CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- 8.2 用户敏感词记录表
CREATE TABLE `user_sensitive_word` (
  `id` bigint AUTO_INCREMENT COMMENT '主键' PRIMARY KEY,
  `user_id` int NOT NULL COMMENT '用户ID',
  `sensitive_word` varchar(100) NOT NULL COMMENT '触发的敏感词',
  `original_content` text COMMENT '原始内容',
  `filtered_content` text COMMENT '过滤后内容',
  `action_taken` varchar(20) COMMENT '采取的动作',
  `module` varchar(50) COMMENT '触发模块',
  `ip_address` varchar(50) COMMENT 'IP地址',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  KEY `idx_user_id` (`user_id`),
  KEY `idx_sensitive_word` (`sensitive_word`),
  KEY `idx_create_time` (`create_time`)
) COMMENT '用户敏感词记录表' CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;
