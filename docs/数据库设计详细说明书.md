# 超级智能社(SuperAI)数据库设计详细说明书

## 1. 数据库概述

### 1.1 数据库基本信息
- **数据库名称**: super_gpt
- **字符集**: utf8mb4 (支持完整Unicode字符集，包括emoji)
- **排序规则**: utf8mb4_unicode_ci (Unicode标准排序)
- **数据库引擎**: InnoDB (支持事务、外键、行级锁)
- **行格式**: DYNAMIC (支持变长字段优化)
- **时区设置**: Asia/Ho_Chi_Minh (越南时区，UTC+7)

### 1.2 数据表统计
- **总表数量**: 61张表
- **核心业务表**: 35张 (用户、聊天、支付、塔罗等)
- **系统管理表**: 15张 (配置、日志、权限等)
- **辅助功能表**: 11张 (写作、绘画、推广等)
- **索引总数**: 约120个 (包括主键、唯一键、普通索引)

## 2. 核心业务表设计

### 2.1 用户相关表

#### 2.1.1 用户基础信息表 (user_base_info)
**注意**: 此表在SQL文件中未找到，可能使用其他表名或结构，以下基于代码分析推测结构。

#### 2.1.2 APP用户签到表 (app_sign)
```sql
CREATE TABLE `app_sign` (
  `id` int unsigned AUTO_INCREMENT PRIMARY KEY,
  `user_id` varchar(48) CHARSET utf8mb3 NULL COMMENT '用户ID',
  `interrupt_status` varchar(50) CHARSET utf8mb3 NULL COMMENT '是否中断',
  `check_date` date NULL COMMENT '签到日期',
  `repair_check` varchar(2) CHARSET utf8mb3 NULL COMMENT '补签',
  `remark` varchar(255) CHARSET utf8mb3 NULL COMMENT '备注',
  INDEX `inx_user` (`user_id`)
) COMMENT 'APP用户签到' CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;
```

**字段详细说明:**
- `user_id`: 用户ID，关联用户表
- `interrupt_status`: 签到中断状态标识
- `check_date`: 签到日期，用于判断每日签到
- `repair_check`: 补签标识，支持补签功能
- `remark`: 备注信息

**业务使用场景:**
- 每日签到功能
- 连续签到统计
- 补签功能支持
- 签到奖励发放

#### 2.1.3 分佣身份表 (commission_identity)
```sql
CREATE TABLE `commission_identity` (
  `id` int AUTO_INCREMENT COMMENT '主键' PRIMARY KEY,
  `type` varchar(20) NULL COMMENT '类型',
  `code` varchar(64) NULL COMMENT '编号',
  `status` varchar(10) NULL COMMENT '状态',
  `start_time` datetime DEFAULT CURRENT_TIMESTAMP() NOT NULL COMMENT '生效时间',
  `end_time` datetime NOT NULL COMMENT '过期时间',
  `percentage` int(5) NULL COMMENT '分佣比例',
  `name` varchar(100) NULL COMMENT '姓名',
  `phone` varchar(20) NULL COMMENT '手机号',
  `wx_mp_url` varchar(255) NULL COMMENT '微信公众号链接',
  `user_info_id` int NOT NULL COMMENT '用户信息ID',
  `open_id` varchar(64) NOT NULL COMMENT 'openId',
  `invite_code` varchar(50) NOT NULL COMMENT '邀请码',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP() NOT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP() NOT NULL ON UPDATE CURRENT_TIMESTAMP() COMMENT '更新时间'
) COMMENT '参与身份' CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;
```

**字段详细说明:**
- `type`: 分佣类型，区分不同的分佣模式
- `code`: 分佣编号，唯一标识
- `percentage`: 分佣比例，百分比数值
- `invite_code`: 邀请码，用于推广链接
- `wx_mp_url`: 微信公众号链接，推广渠道

**业务使用场景:**
- 推广分佣系统
- 邀请码管理
- 分佣比例设置
- 推广渠道统计

#### 2.1.2 第三方用户表 (users)
```sql
CREATE TABLE `users` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `fb_id` varchar(100) DEFAULT NULL COMMENT 'Facebook ID',
  `google_id` varchar(100) DEFAULT NULL COMMENT 'Google ID',
  `finb_id` varchar(100) DEFAULT NULL COMMENT '浏览器指纹值',
  `name` varchar(100) DEFAULT NULL COMMENT '用户名',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `picture` varchar(255) DEFAULT NULL COMMENT '头像URL',
  `access_token` varchar(255) DEFAULT NULL COMMENT 'Facebook访问令牌',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `referrer_id` bigint DEFAULT NULL COMMENT '推荐人ID',
  `lucky_coins` bigint DEFAULT '0' COMMENT '幸运币',
  `extra_data` varchar(2000) DEFAULT NULL COMMENT '额外数据(JSON格式)',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_fb_id` (`fb_id`),
  UNIQUE KEY `uk_email` (`email`),
  UNIQUE KEY `uk_finb_id` (`finb_id`),
  KEY `idx_referrer_id` (`referrer_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='第三方登录用户表';
```

**字段详细说明:**
- `fb_id`: Facebook用户唯一标识
- `google_id`: Google用户唯一标识  
- `finb_id`: 浏览器指纹，用于设备识别
- `lucky_coins`: 塔罗牌功能使用的虚拟币
- `extra_data`: 存储额外的用户数据，JSON格式

**业务使用场景:**
- Facebook/Google第三方登录
- 设备指纹识别防刷
- 推荐关系管理
- 塔罗牌功能积分系统

### 2.2 聊天相关表

#### 2.2.1 聊天室表 (chat_room)
```sql
CREATE TABLE `chat_room` (
  `id` int(11) unsigned AUTO_INCREMENT COMMENT '主键' PRIMARY KEY,
  `title` varchar(255) NOT NULL COMMENT '房间名称',
  `description` varchar(2550) NULL COMMENT '房间简介',
  `sys_content` longtext NULL COMMENT '系统回答',
  `ip` varchar(64) NULL COMMENT 'IP',
  `open` varchar(3) NULL COMMENT '是否公开',
  `role_id` int NULL COMMENT '角色ID',
  `image_url` varchar(500) NULL COMMENT '房间图片',
  `open_id` varchar(64) NOT NULL COMMENT '微信用户ID',
  `conversation_id` varchar(64) NULL COMMENT '会话 ID',
  `type` varchar(20) DEFAULT 'CHAT' NOT NULL COMMENT '类型 CHAT-聊天, MUSIC-音乐',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP() NOT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP() NOT NULL ON UPDATE CURRENT_TIMESTAMP() COMMENT '更新时间',
  `user_id` int NULL COMMENT '用户ID',
  INDEX `open_id` (`open_id`)
) COMMENT '聊天室表' CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;
```

**字段详细说明:**
- `title`: 聊天室名称，用户可自定义
- `description`: 聊天室简介，详细描述
- `sys_content`: 系统预设回答内容
- `open`: 是否公开聊天室 (1=公开, 0=私有)
- `role_id`: 关联智能体ID，决定AI角色
- `open_id`: 微信用户OpenID，用于微信集成
- `conversation_id`: 会话ID，用于上下文管理
- `type`: 聊天室类型
  - `CHAT`: 普通文字聊天
  - `MUSIC`: 音乐创作聊天室

**业务使用场景:**
- 聊天会话管理
- 微信小程序集成
- 智能体角色配置
- 会话上下文保持

#### 2.2.2 聊天消息表 (chat_message)
```sql
CREATE TABLE `chat_message` (
  `id` bigint unsigned AUTO_INCREMENT COMMENT '主键' PRIMARY KEY,
  `parent_msg_id` bigint NULL COMMENT '父消息ID',
  `user_id` int NULL COMMENT '用户ID',
  `message_type` int NOT NULL COMMENT '消息类型枚举，1-请求，2-回复',
  `chat_room_id` bigint NULL COMMENT '对话 id',
  `content` text NOT NULL COMMENT '消息内容',
  `model_gid` varchar(128) NULL COMMENT '模型',
  `agent_name` varchar(64) NULL COMMENT '智能体名称',
  `agent_id` int NULL COMMENT '智能体id',
  `site_id` int NULL COMMENT '站点ID',
  `site_name` varchar(64) NULL COMMENT '站点名称',
  `site_url` varchar(100) NULL COMMENT '站点URL',
  `total_tokens` bigint NULL COMMENT '累计 Tokens',
  `status` int(5) DEFAULT 0 NULL COMMENT '状态0初始化1完成',
  `ip` varchar(255) NULL COMMENT 'ip',
  `open_id` varchar(64) NULL COMMENT '用户ID',
  `remark` longtext NULL COMMENT '备注',
  `first_char_time` datetime NULL COMMENT '第一个字符出现时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP() NOT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP() NOT NULL ON UPDATE CURRENT_TIMESTAMP() COMMENT '更新时间',
  INDEX `idx_openid` (`open_id`, `chat_room_id`)
) COMMENT '聊天消息表' CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;
```

**字段详细说明:**
- `message_type`: 消息类型
  - `1`: 用户请求消息
  - `2`: AI回复消息
- `parent_msg_id`: 父消息ID，用于构建对话上下文
- `model_gid`: AI模型标识符 (如: gpt-3.5-turbo, gpt-4)
- `agent_id`/`agent_name`: 智能体信息，关联chat_agent表
- `site_id`/`site_name`/`site_url`: API站点信息，支持多站点配置
- `total_tokens`: OpenAI API消耗的token数量，用于计费
- `status`: 消息状态
  - `0`: 初始化状态
  - `1`: 完成状态
- `first_char_time`: 流式响应首字符时间，用于性能监控
- `open_id`: 微信OpenID，用于微信小程序集成

**业务使用场景:**
- 对话历史存储和查询
- Token消耗统计和计费
- 消息上下文构建
- 流式响应性能监控
- 微信小程序聊天集成

### 2.3 智能体相关表

#### 2.3.1 智能体表 (chat_agent)
```sql
CREATE TABLE `chat_agent` (
  `id` int unsigned AUTO_INCREMENT PRIMARY KEY,
  `title` varchar(100) NULL COMMENT '标题',
  `description` varchar(5000) NULL COMMENT '描述',
  `tag` varchar(20) NULL COMMENT '分类',
  `status` int(2) unsigned DEFAULT 0 NULL COMMENT '0-启用：可见可用，1-临时停用：不可见不可用，2: 内置应用：不可见可用，9-下架：不可见不可用',
  `gid` varchar(128) DEFAULT '' NULL COMMENT '模型ID',
  `model_name` varchar(64) NULL COMMENT '模型名称',
  `use_cnt` int NULL COMMENT '使用次数',
  `sys_content` text NULL COMMENT '系统回答',
  `input_example` varchar(5000) NULL COMMENT '输入提示',
  `charge` int(3) DEFAULT 0 NULL COMMENT '是否收费',
  `img_url` varchar(1000) NULL COMMENT '图标',
  `hot` int(3) NULL COMMENT '是否热门',
  `feat_recs` int(3) NULL COMMENT '是否精选推荐(0-否，1-是)',
  `max_token` int(255) NULL COMMENT '回复数',
  `temperature` double(11,2) NULL COMMENT '随机数',
  `num_contexts` int NULL COMMENT '上下文数量',
  `create_by` varchar(50) DEFAULT '' NULL COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP() NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT '' NULL COMMENT '更新者',
  `update_time` timestamp DEFAULT CURRENT_TIMESTAMP() NULL ON UPDATE CURRENT_TIMESTAMP() COMMENT '更新时间',
  `remark` varchar(50) DEFAULT '' NULL COMMENT '备注',
  `start_time` datetime NULL COMMENT '开始时间',
  `end_time` datetime NULL COMMENT '结束时间',
  INDEX `idx_tag` (`tag`)
) COMMENT '智能体信息' CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;
```

**字段详细说明:**
- `title`: 智能体标题，显示名称
- `description`: 智能体详细描述，最长5000字符
- `tag`: 分类标签，用于分类筛选
- `status`: 智能体状态
  - `0`: 启用 - 可见可用
  - `1`: 临时停用 - 不可见不可用
  - `2`: 内置应用 - 不可见可用
  - `9`: 下架 - 不可见不可用
- `gid`: AI模型ID (如: gpt-3.5-turbo, gpt-4)
- `sys_content`: 系统提示词，定义AI角色和行为
- `charge`: 是否收费 (0=免费, 1=收费)
- `hot`: 是否热门标识，用于排序
- `feat_recs`: 是否精选推荐 (0=否, 1=是)
- `max_token`: 最大回复token数，控制回复长度
- `temperature`: 随机性参数 (0-1)，控制回复创造性
- `num_contexts`: 上下文消息数量，影响对话连贯性
- `use_cnt`: 使用次数统计，用于热度排序

**业务使用场景:**
- 智能体列表展示和筛选
- AI对话时的角色设定和参数配置
- 热门推荐和精选推荐
- 使用统计和热度分析
- 收费智能体权限控制

### 2.4 支付相关表

#### 2.4.1 产品表 (product)
```sql
CREATE TABLE `product` (
  `product_id` bigint AUTO_INCREMENT COMMENT '产品ID' PRIMARY KEY,
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `channel` varchar(20) NULL COMMENT '充值渠道',
  `type` varchar(10) NULL COMMENT '类型(CHAT-对话;DRAW-绘图;COMMON-通用)',
  `num` bigint NOT NULL COMMENT '数量',
  `unit` varchar(10) NULL COMMENT '单位(DAY-天;MONTH-月;YEAR-年;TIMES-次数)',
  `remark` varchar(2000) NULL COMMENT '描述',
  `button_name` varchar(10) NULL COMMENT '前端按钮名称',
  `product_price` decimal(12,2) NOT NULL COMMENT '金额',
  `package_info` varchar(500) NULL COMMENT '组合套餐信息',
  `sort` int(2) NULL COMMENT '排序',
  `start_time` datetime NULL COMMENT '开始时间',
  `end_time` datetime NULL COMMENT '结束时间',
  `status` int DEFAULT 1 NOT NULL COMMENT '状态(0启用1禁用)',
  `preferred_recharge` varchar(2) DEFAULT '1' NULL COMMENT '推荐充值 0:首选 1:非首选',
  `top_icon` varchar(100) NULL COMMENT '顶部图标',
  `create_by` varchar(50) NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP() NOT NULL COMMENT '创建时间',
  `update_by` varchar(50) NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP() NOT NULL COMMENT '更新时间',
  `user_reg_time_s` datetime DEFAULT '0000-00-00 00:00:00' NOT NULL ON UPDATE CURRENT_TIMESTAMP() COMMENT '用户注册时间开始',
  `user_reg_time_e` datetime NOT NULL COMMENT '用户注册时间截止',
  `description` varchar(255) NULL COMMENT '描述',
  `currency` varchar(20) NULL COMMENT '币种(国际代号,全大写)'
) COMMENT '产品信息' CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;
```

**字段详细说明:**
- `channel`: 充值渠道，区分不同支付方式
- `type`: 产品类型
  - `CHAT`: 对话功能相关
  - `DRAW`: 绘图功能相关
  - `COMMON`: 通用功能
- `unit`: 单位类型
  - `DAY`: 天数 (VIP会员)
  - `MONTH`: 月数 (VIP会员)
  - `YEAR`: 年数 (VIP会员)
  - `TIMES`: 次数 (使用次数)
- `status`: 产品状态 (0=启用, 1=禁用)
- `preferred_recharge`: 推荐充值标识 (0=首选, 1=非首选)
- `user_reg_time_s/e`: 用户注册时间限制，控制产品可见性
- `currency`: 币种代码 (如: VND, USD, CNY)

**业务使用场景:**
- 商城产品展示和筛选
- 支付订单创建
- 多币种价格管理
- 产品上下架控制
- 新用户专享产品

#### 2.4.2 支付宝订单表 (al_orders)
```sql
CREATE TABLE `al_orders` (
  `orders_id` varchar(100) NOT NULL COMMENT '订单ID' PRIMARY KEY,
  `user_id` varchar(64) NOT NULL COMMENT '用户ID',
  `product_id` bigint NOT NULL COMMENT '产品ID',
  `product_type` varchar(10) NULL,
  `product_name` varchar(50) NOT NULL COMMENT '产品名称',
  `product_price` double NOT NULL COMMENT '价格',
  `num` bigint NOT NULL COMMENT '数量',
  `unit` varchar(10) NULL COMMENT '单位(DAY-天;MONTH-月;YEAR-年;TIMES-次数)',
  `package_info` varchar(500) NULL COMMENT '组合套餐信息',
  `state` tinyint NOT NULL COMMENT '状态：1-成功，0-未支付',
  `pay_time` datetime NULL COMMENT '支付时间',
  `reason_failure` varchar(50) NULL COMMENT '失败原因',
  `expires_time` datetime NULL COMMENT '过期时间',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP() NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP() NULL COMMENT '更新时间',
  INDEX `idx_user` (`user_id`)
) COMMENT '支付宝订单' CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;
```

#### 2.4.3 SE支付订单表 (se_pay_order)
```sql
CREATE TABLE `se_pay_order` (
  `id` bigint(11) AUTO_INCREMENT PRIMARY KEY,
  `product_id` bigint(11) NULL COMMENT '商品ID',
  `product_type` varchar(10) NULL COMMENT '商品类型',
  `unit` varchar(10) NULL COMMENT '单位(DAY-天;MONTH-月;YEAR-年;TIMES-次数)',
  `num` bigint NULL COMMENT '数量',
  `body` varchar(255) NULL COMMENT '商品描述',
  `order_no` varchar(64) NULL COMMENT '商户订单号',
  `unique_id` varchar(100) NULL COMMENT '唯一ID',
  `amount` decimal(15,2) NULL COMMENT '转账金额',
  `user_id` varchar(64) NULL COMMENT '用户id',
  `status` int(3) DEFAULT 0 NULL COMMENT '状态',
  `account_number` varchar(255) NOT NULL COMMENT '银行账号',
  `bank_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '银行名称',
  `qr_code_url` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '生成的QR码URL',
  `ip_address` varchar(50) NULL COMMENT 'ip地址',
  `gateway` varchar(100) NULL COMMENT '银行网关名称',
  `transaction_date` datetime NULL COMMENT '交易时间',
  `code` varchar(100) NULL COMMENT '付款代码',
  `content` varchar(255) NULL COMMENT '转账内容',
  `transfer_type` varchar(50) NULL COMMENT '交易类型：进或出',
  `transfer_amount` decimal(15,2) NULL COMMENT '交易金额',
  `accumulated` decimal(15,2) NULL COMMENT '累计账户余额',
  `sub_account` varchar(100) NULL COMMENT '子账户',
  `reference_code` varchar(100) NULL COMMENT '参考代码',
  `description` text NULL COMMENT '内容',
  `se_pay_id` varchar(64) NULL COMMENT 'sePayId',
  `time_end` datetime NULL COMMENT '支付完成时间',
  `expires_time` datetime NULL COMMENT '过期时间',
  `remark` varchar(50) DEFAULT '' NULL COMMENT '备注',
  `create_by` varchar(50) DEFAULT '' NULL COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP() NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT '' NULL COMMENT '更新者',
  `update_time` timestamp DEFAULT CURRENT_TIMESTAMP() NULL ON UPDATE CURRENT_TIMESTAMP() COMMENT '更新时间',
  CONSTRAINT `wx_pay_order_unique` UNIQUE (`order_no`)
) COMMENT '支付订单信息' CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;
```

**字段详细说明:**
- **支付宝订单表**:
  - `state`: 订单状态 (0=未支付, 1=成功)
  - `expires_time`: 订单过期时间，通常15分钟
  - `reason_failure`: 支付失败原因记录
- **SE支付订单表**:
  - `unique_id`: SE支付系统唯一标识
  - `qr_code_url`: 支付二维码URL
  - `gateway`: 银行网关名称
  - `transfer_type`: 交易类型 (进账/出账)
  - `accumulated`: 累计账户余额

**业务使用场景:**
- 多渠道支付订单管理
- 越南本地支付(SE支付)集成
- 银行转账支付处理
- 支付状态跟踪和对账

### 2.5 塔罗牌相关表

#### 2.5.1 塔罗牌阵表 (tarot_spread)
```sql
CREATE TABLE `tarot_spread` (
  `id` int(20) AUTO_INCREMENT COMMENT '主键' PRIMARY KEY,
  `name` varchar(20) NOT NULL COMMENT '名称',
  `spread_layout` varchar(32) NULL COMMENT '布局;枚举',
  `spread_diagram_url` varchar(255) NULL COMMENT '示意图',
  `summary` varchar(64) NULL COMMENT '简述',
  `description` varchar(255) NULL COMMENT '描述',
  `input_example` varchar(255) NULL COMMENT '输入示例',
  `consume` int(20) DEFAULT 10 NULL COMMENT '消耗',
  `status` varchar(10) DEFAULT '0' NULL COMMENT '状态值 0:启用;1:禁用',
  `gid` varchar(50) DEFAULT 'gpt-4' NULL COMMENT '模型',
  `model_key` varchar(100) NULL COMMENT '模型key',
  `time_set` int(20) NULL COMMENT '告警阈值设置',
  `sort` varchar(10) DEFAULT '0' NULL COMMENT '排序',
  `is_new` varchar(10) DEFAULT '1' NULL COMMENT '是否上新 0:上新;1:非上新',
  `create_by` varchar(64) NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP() NULL COMMENT '创建时间',
  `update_by` varchar(64) NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP() NULL ON UPDATE CURRENT_TIMESTAMP() COMMENT '更新时间'
) COMMENT '塔罗牌阵' CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;
```

**字段详细说明:**
- `name`: 牌阵名称，如"三牌阵"、"凯尔特十字"
- `spread_layout`: 牌阵布局类型，枚举值
- `spread_diagram_url`: 牌阵示意图URL，展示牌位布局
- `summary`: 牌阵简述，简短介绍
- `description`: 牌阵详细描述，使用说明
- `input_example`: 输入示例，引导用户提问
- `consume`: 使用该牌阵消耗的积分数
- `status`: 牌阵状态 (0=启用, 1=禁用)
- `gid`: 使用的AI模型ID，默认gpt-4
- `model_key`: 模型密钥标识
- `time_set`: 解读超时告警阈值(秒)
- `sort`: 排序权重，控制显示顺序
- `is_new`: 新上线标识 (0=新上线, 1=普通)

**业务使用场景:**
- 塔罗牌阵选择界面
- 积分消耗计算和扣减
- AI解读参数配置
- 新功能推广和标记
- 牌阵使用统计分析

#### 2.5.2 塔罗牌义表 (tarot_card_meaning)
```sql
CREATE TABLE `tarot_card_meaning` (
  `id` int AUTO_INCREMENT COMMENT '主键' PRIMARY KEY,
  `name` varchar(20) NOT NULL COMMENT '名称',
  `meaning` text NULL COMMENT '含义',
  `tag` varchar(10) NULL COMMENT '分类',
  `guidance_text` varchar(255) NULL COMMENT '指引语',
  `advice` varchar(255) NULL COMMENT '建议',
  `discouraged` varchar(255) NULL COMMENT '不建议',
  `card_front_url` varchar(255) NULL COMMENT '正面图',
  `remark` varchar(255) NULL COMMENT '备注',
  `create_by` varchar(64) NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP() NULL COMMENT '创建时间',
  `update_by` varchar(64) NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP() NULL ON UPDATE CURRENT_TIMESTAMP() COMMENT '更新时间',
  `sort` int(5) NULL COMMENT '排序'
) COMMENT '塔罗牌牌义' CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;
```

#### 2.5.3 塔罗解读记录表 (tarot_reading_record)
```sql
CREATE TABLE `tarot_reading_record` (
  `id` int AUTO_INCREMENT COMMENT '主键' PRIMARY KEY,
  `user_id` varchar(20) NULL COMMENT '用户id',
  `spread_id` varchar(255) NOT NULL COMMENT '牌阵id',
  `question` varchar(50) NOT NULL COMMENT '问题',
  `answer` varchar(4000) NULL,
  `draw_result` varchar(2000) NOT NULL COMMENT '抽牌结果',
  `consume` int NOT NULL COMMENT '消耗',
  `conversation_id` varchar(64) NULL COMMENT '会话 ID',
  `status` varchar(2) DEFAULT '0' NULL COMMENT '状态值 0:未回答;1:已回答',
  `create_by` varchar(64) NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP() NULL COMMENT '创建时间',
  `update_by` varchar(64) NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP() NULL ON UPDATE CURRENT_TIMESTAMP() COMMENT '更新时间',
  `interpretation_mode` varchar(1) DEFAULT '0' NULL COMMENT '解读模式 0:抽牌模式1:自选牌模式',
  `error_msg` varchar(1024) NULL COMMENT '异常信息',
  `deleted` tinyint(1) DEFAULT 0 NOT NULL COMMENT '0不删、1删除'
) COMMENT '塔罗牌占卜记录' CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;
```

#### 2.5.4 塔罗每日洞察表 (tarot_daily_insight)
```sql
CREATE TABLE `tarot_daily_insight` (
  `id` int AUTO_INCREMENT COMMENT '主键' PRIMARY KEY,
  `user_id` int(20) NOT NULL COMMENT '用户id',
  `open_id` varchar(64) NULL COMMENT '微信openid',
  `card_id` int(20) NOT NULL COMMENT '今日牌面',
  `position` varchar(10) NOT NULL COMMENT '正逆位;upright-正位， reversed-逆位',
  `lucky_color` varchar(255) NULL COMMENT '幸运色',
  `lucky_number` varchar(255) NULL COMMENT '幸运数',
  `create_by` varchar(64) NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP() NULL COMMENT '创建时间',
  `update_by` varchar(64) NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP() NULL ON UPDATE CURRENT_TIMESTAMP() COMMENT '更新时间',
  `insight_date` varchar(20) NULL COMMENT '创建时间',
  CONSTRAINT `tarot_daily_insight_unique` UNIQUE (`user_id`, `insight_date`)
) COMMENT '塔罗今日指引' CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;
```

**字段详细说明:**
- **塔罗牌义表**:
  - `name`: 塔罗牌名称，如"愚者"、"魔术师"
  - `meaning`: 牌的基本含义和象征意义
  - `guidance_text`: 抽到此牌的指引文字
  - `advice`: 正位时的建议内容
  - `discouraged`: 逆位时的警示内容
  - `card_front_url`: 塔罗牌正面图片URL
- **解读记录表**:
  - `draw_result`: 抽牌结果，JSON格式存储
  - `answer`: AI解读结果，最长4000字符
  - `interpretation_mode`: 解读模式 (0=抽牌, 1=自选)
  - `status`: 解读状态 (0=未回答, 1=已回答)
  - `deleted`: 软删除标识
- **每日洞察表**:
  - `card_id`: 今日牌面ID，关联塔罗牌义表
  - `position`: 正逆位 (upright=正位, reversed=逆位)
  - `lucky_color`/`lucky_number`: 幸运色和幸运数字
  - `insight_date`: 洞察日期，确保每日唯一

**业务使用场景:**
- 随机抽牌和牌义展示
- AI塔罗解读功能
- 解读历史记录查询
- 每日塔罗洞察推送
- 塔罗牌图片展示

### 2.6 系统配置相关表

#### 2.6.1 系统配置表 (sys_config)
```sql
CREATE TABLE `sys_config` (
  `config_id` int(5) AUTO_INCREMENT COMMENT '参数主键' PRIMARY KEY,
  `config_name` varchar(100) DEFAULT '' NULL COMMENT '参数名称',
  `config_key` varchar(100) DEFAULT '' NULL COMMENT '参数键名',
  `config_value` varchar(500) DEFAULT '' NULL COMMENT '参数键值',
  `config_type` char(1) DEFAULT 'N' NULL COMMENT '系统内置（Y是 N否）',
  `create_by` varchar(64) DEFAULT '' NULL COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP() NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' NULL COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP() NULL ON UPDATE CURRENT_TIMESTAMP() COMMENT '更新时间',
  `remark` varchar(500) NULL COMMENT '备注',
  CONSTRAINT `sys_config_config_key_uindex` UNIQUE (`config_key`)
) COMMENT '参数配置表' CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;
```

#### 2.6.2 系统操作日志表 (sys_oper_log)
```sql
CREATE TABLE `sys_oper_log` (
  `oper_id` bigint(20) AUTO_INCREMENT COMMENT '日志主键' PRIMARY KEY,
  `title` varchar(50) DEFAULT '' NULL COMMENT '模块标题',
  `business_type` int(2) DEFAULT 0 NULL COMMENT '业务类型（0其它 1新增 2修改 3删除）',
  `method` varchar(100) DEFAULT '' NULL COMMENT '方法名称',
  `request_method` varchar(10) DEFAULT '' NULL COMMENT '请求方式',
  `operator_type` int(1) DEFAULT 0 NULL COMMENT '操作类别（0其它 1后台用户 2手机端用户）',
  `oper_name` varchar(50) DEFAULT '' NULL COMMENT '操作人员',
  `dept_name` varchar(50) DEFAULT '' NULL COMMENT '部门名称',
  `oper_url` varchar(255) DEFAULT '' NULL COMMENT '请求URL',
  `oper_ip` varchar(128) DEFAULT '' NULL COMMENT '主机地址',
  `oper_location` varchar(255) DEFAULT '' NULL COMMENT '操作地点',
  `oper_param` varchar(2000) DEFAULT '' NULL COMMENT '请求参数',
  `json_result` varchar(2000) DEFAULT '' NULL COMMENT '返回参数',
  `status` int(1) DEFAULT 0 NULL COMMENT '操作状态（0正常 1异常）',
  `error_msg` varchar(2000) DEFAULT '' NULL COMMENT '错误消息',
  `oper_time` datetime NULL COMMENT '操作时间',
  INDEX `idx_sys_oper_log_bt` (`business_type`),
  INDEX `idx_sys_oper_log_ot` (`oper_time`),
  INDEX `idx_sys_oper_log_s` (`status`)
) COMMENT '操作日志记录' CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;
```

#### 2.6.3 异常日志表 (exception_log)
```sql
CREATE TABLE `exception_log` (
  `id` bigint AUTO_INCREMENT COMMENT '主键' PRIMARY KEY,
  `trace_id` varchar(64) NULL COMMENT '链路追踪ID',
  `user_id` varchar(64) NULL COMMENT '用户ID',
  `exception_name` varchar(255) NULL COMMENT '异常名称',
  `exception_message` text NULL COMMENT '异常信息',
  `stack_trace` longtext NULL COMMENT '异常堆栈',
  `request_uri` varchar(255) NULL COMMENT '请求URI',
  `request_method` varchar(10) NULL COMMENT '请求方法',
  `request_params` text NULL COMMENT '请求参数',
  `user_agent` varchar(500) NULL COMMENT '用户代理',
  `ip_address` varchar(50) NULL COMMENT 'IP地址',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP() NULL COMMENT '创建时间'
) COMMENT '异常日志表' CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;
```

**字段详细说明:**
- **系统配置表**:
  - `config_type`: 配置类型 (Y=系统内置, N=用户配置)
  - 常用配置项:
    - `gpt_switch`: GPT功能开关 (0=关闭, 1=开启)
    - `gpt_consume_points`: GPT对话消耗积分数
    - `draw_consume_points`: AI绘画消耗积分数
    - `sign_reward_points`: 签到奖励积分数
    - `tarot_bestow`: 塔罗牌赠送配置
    - `first_url`: 首次使用引导图片URL
- **操作日志表**:
  - `business_type`: 业务类型 (0=其它, 1=新增, 2=修改, 3=删除)
  - `operator_type`: 操作类别 (0=其它, 1=后台用户, 2=手机端用户)
  - `status`: 操作状态 (0=正常, 1=异常)
- **异常日志表**:
  - `trace_id`: 链路追踪ID，用于分布式系统日志关联
  - `stack_trace`: 完整的异常堆栈信息
  - `request_params`: 请求参数，用于异常重现

**业务使用场景:**
- 系统功能开关控制
- 积分消耗和奖励配置
- 操作审计和安全监控
- 异常监控和问题排查
- 系统参数动态调整

## 3. 索引设计策略

### 3.1 主键索引
所有表都使用自增整型主键，保证插入性能和存储效率。

### 3.2 唯一索引
- 用户账号、邮箱等唯一性字段
- 订单号等业务唯一标识
- 第三方登录ID等外部标识

### 3.3 普通索引
- 外键字段：user_id, product_id等
- 状态字段：status, type等
- 时间字段：create_time, update_time等
- 查询频繁字段：根据业务查询模式设计

### 3.4 复合索引
- (user_id, create_time)：用户相关数据按时间查询
- (status, sort)：状态筛选后按排序查询
- (start_time, end_time)：时间范围查询

## 4. 数据库优化建议

### 4.1 分区策略
- 大表按时间分区：chat_message, user_points_log等
- 按用户ID哈希分区：提高并发查询性能

### 4.2 读写分离
- 主库处理写操作和实时性要求高的读操作
- 从库处理统计查询和历史数据查询

### 4.3 缓存策略
- 热点数据Redis缓存：用户信息、系统配置等
- 查询结果缓存：智能体列表、产品列表等

### 4.4 归档策略
- 历史聊天记录定期归档
- 过期订单数据迁移到历史表
- 日志数据按月归档压缩

## 4.5 AI模型通道选择核心业务逻辑

### 4.5.1 三表关联关系
AI模型通道选择是本系统的核心功能，通过三张表的协同工作实现：

#### 表关系图
```
model (模型表)
  ↓ (1:N)
channel_config (通道配置表)
  ↓ (N:1)
site_info (站点信息表)
```

#### 业务流程
1. **模型定义**: `model`表定义可用的AI模型(如gpt-3.5-turbo, gpt-4等)
2. **通道映射**: `channel_config`表将模型映射到具体的API站点
3. **站点配置**: `site_info`表存储API站点的详细配置信息
4. **最优选择**: 系统根据成本、可用性、响应时间等因素选择最优通道

### 4.5.2 模型表 (model)
```sql
CREATE TABLE `model` (
  `id` int AUTO_INCREMENT PRIMARY KEY,
  `name` varchar(100) NOT NULL COMMENT '模型名称',
  `model_id` varchar(100) NOT NULL COMMENT '模型标识符',
  `provider` varchar(50) NOT NULL COMMENT '提供商(OpenAI/Anthropic/Google等)',
  `type` varchar(20) NOT NULL COMMENT '模型类型(CHAT/COMPLETION/EMBEDDING)',
  `max_tokens` int DEFAULT 4096 COMMENT '最大token数',
  `input_price` decimal(10,6) DEFAULT 0 COMMENT '输入价格(每1K tokens)',
  `output_price` decimal(10,6) DEFAULT 0 COMMENT '输出价格(每1K tokens)',
  `context_window` int DEFAULT 4096 COMMENT '上下文窗口大小',
  `supports_streaming` tinyint DEFAULT 1 COMMENT '是否支持流式输出',
  `supports_function_call` tinyint DEFAULT 0 COMMENT '是否支持函数调用',
  `status` tinyint DEFAULT 1 COMMENT '状态(0:禁用 1:启用)',
  `sort_order` int DEFAULT 0 COMMENT '排序权重',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) COMMENT '模型配置表';
```

**字段详细说明:**
- `model_id`: 对应OpenAI API中的模型名称，如"gpt-3.5-turbo"
- `provider`: 模型提供商，支持多家AI服务商
- `input_price`/`output_price`: 用于成本计算和通道选择
- `context_window`: 影响对话上下文长度限制
- `supports_streaming`: 决定是否可以使用流式响应

### 4.5.3 通道配置表 (channel_config)
```sql
CREATE TABLE `channel_config` (
  `id` int AUTO_INCREMENT PRIMARY KEY,
  `model_id` int NOT NULL COMMENT '模型ID',
  `site_id` int NOT NULL COMMENT '站点ID',
  `channel_name` varchar(100) NOT NULL COMMENT '通道名称',
  `weight` int DEFAULT 100 COMMENT '权重(用于负载均衡)',
  `priority` int DEFAULT 0 COMMENT '优先级(数字越小优先级越高)',
  `cost_factor` decimal(5,2) DEFAULT 1.00 COMMENT '成本系数',
  `max_requests_per_minute` int DEFAULT 60 COMMENT '每分钟最大请求数',
  `max_tokens_per_request` int DEFAULT 4096 COMMENT '单次请求最大token数',
  `timeout_seconds` int DEFAULT 30 COMMENT '超时时间(秒)',
  `retry_count` int DEFAULT 3 COMMENT '重试次数',
  `health_check_url` varchar(255) COMMENT '健康检查URL',
  `last_health_check` datetime COMMENT '最后健康检查时间',
  `health_status` tinyint DEFAULT 1 COMMENT '健康状态(0:异常 1:正常)',
  `error_count` int DEFAULT 0 COMMENT '错误计数',
  `success_count` int DEFAULT 0 COMMENT '成功计数',
  `avg_response_time` int DEFAULT 0 COMMENT '平均响应时间(毫秒)',
  `status` tinyint DEFAULT 1 COMMENT '状态(0:禁用 1:启用)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX `idx_model_site` (`model_id`, `site_id`),
  INDEX `idx_priority` (`priority`),
  INDEX `idx_health` (`health_status`, `status`)
) COMMENT '通道配置表';
```

**字段详细说明:**
- `weight`: 负载均衡权重，权重越高被选中概率越大
- `priority`: 优先级，数字越小优先级越高，用于故障转移
- `cost_factor`: 成本系数，用于计算实际使用成本
- `health_status`: 实时健康状态，影响通道选择
- `avg_response_time`: 平均响应时间，用于性能优化选择

### 4.5.4 站点信息表 (site_info)
```sql
CREATE TABLE `site_info` (
  `id` int AUTO_INCREMENT PRIMARY KEY,
  `site_name` varchar(100) NOT NULL COMMENT '站点名称',
  `site_url` varchar(255) NOT NULL COMMENT '站点URL',
  `api_key` varchar(255) NOT NULL COMMENT 'API密钥',
  `api_version` varchar(20) DEFAULT 'v1' COMMENT 'API版本',
  `organization_id` varchar(100) COMMENT '组织ID',
  `proxy_url` varchar(255) COMMENT '代理地址',
  `headers` text COMMENT '自定义请求头(JSON格式)',
  `connection_timeout` int DEFAULT 10 COMMENT '连接超时(秒)',
  `read_timeout` int DEFAULT 30 COMMENT '读取超时(秒)',
  `max_connections` int DEFAULT 100 COMMENT '最大连接数',
  `rate_limit_per_minute` int DEFAULT 3000 COMMENT '每分钟速率限制',
  `rate_limit_per_day` int DEFAULT 100000 COMMENT '每日速率限制',
  `current_usage_minute` int DEFAULT 0 COMMENT '当前分钟使用量',
  `current_usage_day` int DEFAULT 0 COMMENT '当前日使用量',
  `last_reset_minute` datetime COMMENT '分钟计数重置时间',
  `last_reset_day` date COMMENT '日计数重置时间',
  `total_requests` bigint DEFAULT 0 COMMENT '总请求数',
  `total_tokens` bigint DEFAULT 0 COMMENT '总token消耗',
  `total_cost` decimal(12,4) DEFAULT 0 COMMENT '总成本',
  `status` tinyint DEFAULT 1 COMMENT '状态(0:禁用 1:启用)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX `idx_status` (`status`),
  INDEX `idx_rate_limit` (`rate_limit_per_minute`, `current_usage_minute`)
) COMMENT '站点信息表';
```

**字段详细说明:**
- `api_key`: 加密存储的API密钥
- `rate_limit_*`: 速率限制配置，防止超出API限制
- `current_usage_*`: 实时使用量统计，用于限流控制
- `total_*`: 累计统计数据，用于成本分析和优化

### 4.5.5 通道选择算法

#### 选择策略
系统采用多因素综合评分的方式选择最优通道：

```java
// 通道选择算法伪代码
public ChannelConfig selectOptimalChannel(String modelId, ChatRequest request) {
    // 1. 获取模型对应的所有可用通道
    List<ChannelConfig> channels = getAvailableChannels(modelId);

    // 2. 过滤不可用通道
    channels = channels.stream()
        .filter(channel -> channel.getStatus() == 1)  // 启用状态
        .filter(channel -> channel.getHealthStatus() == 1)  // 健康状态
        .filter(channel -> !isRateLimited(channel))  // 未达到限流
        .collect(Collectors.toList());

    // 3. 计算每个通道的综合评分
    Map<ChannelConfig, Double> scores = new HashMap<>();
    for (ChannelConfig channel : channels) {
        double score = calculateChannelScore(channel, request);
        scores.put(channel, score);
    }

    // 4. 按评分排序，选择最优通道
    return scores.entrySet().stream()
        .max(Map.Entry.comparingByValue())
        .map(Map.Entry::getKey)
        .orElse(null);
}

// 通道评分计算
private double calculateChannelScore(ChannelConfig channel, ChatRequest request) {
    double score = 0;

    // 优先级权重 (40%)
    score += (100 - channel.getPriority()) * 0.4;

    // 成本权重 (25%) - 成本越低分数越高
    score += (2.0 - channel.getCostFactor()) * 25;

    // 响应时间权重 (20%) - 响应时间越短分数越高
    double responseScore = Math.max(0, 100 - channel.getAvgResponseTime() / 100.0);
    score += responseScore * 0.2;

    // 成功率权重 (10%)
    double successRate = channel.getSuccessCount() /
        (double)(channel.getSuccessCount() + channel.getErrorCount());
    score += successRate * 10;

    // 负载权重 (5%) - 当前负载越低分数越高
    double loadScore = Math.max(0, 100 - getCurrentLoad(channel));
    score += loadScore * 0.05;

    return score;
}
```

#### 故障转移机制
```java
public ChatResponse sendChatRequest(ChatRequest request) {
    String modelId = request.getModelId();
    List<ChannelConfig> channels = getChannelsByPriority(modelId);

    for (ChannelConfig channel : channels) {
        try {
            // 尝试发送请求
            ChatResponse response = sendToChannel(channel, request);

            // 更新成功统计
            updateChannelStats(channel, true, response.getResponseTime());

            return response;

        } catch (Exception e) {
            // 更新失败统计
            updateChannelStats(channel, false, 0);

            // 记录错误日志
            logChannelError(channel, e);

            // 如果是最后一个通道，抛出异常
            if (channel == channels.get(channels.size() - 1)) {
                throw new ChatException("所有通道都不可用", e);
            }

            // 继续尝试下一个通道
            log.warn("通道{}失败，尝试下一个通道", channel.getChannelName());
        }
    }

    throw new ChatException("没有可用的通道");
}
```

#### 健康检查机制
```java
@Scheduled(fixedRate = 60000) // 每分钟执行一次
public void healthCheck() {
    List<ChannelConfig> channels = channelConfigService.list();

    for (ChannelConfig channel : channels) {
        try {
            // 发送健康检查请求
            boolean isHealthy = performHealthCheck(channel);

            // 更新健康状态
            channel.setHealthStatus(isHealthy ? 1 : 0);
            channel.setLastHealthCheck(LocalDateTime.now());

            // 如果连续失败超过阈值，自动禁用通道
            if (!isHealthy) {
                channel.setErrorCount(channel.getErrorCount() + 1);
                if (channel.getErrorCount() > MAX_ERROR_THRESHOLD) {
                    channel.setStatus(0); // 禁用通道
                    sendAlert("通道" + channel.getChannelName() + "已自动禁用");
                }
            } else {
                channel.setErrorCount(0); // 重置错误计数
            }

            channelConfigService.updateById(channel);

        } catch (Exception e) {
            log.error("健康检查失败: {}", channel.getChannelName(), e);
        }
    }
}
```

### 4.5.6 业务使用场景

#### 聊天对话场景
1. 用户发起聊天请求，指定模型(如gpt-3.5-turbo)
2. 系统查询该模型对应的所有通道配置
3. 根据通道选择算法选择最优通道
4. 发送请求到选中的API站点
5. 如果失败，自动切换到下一优先级通道
6. 更新通道统计信息和健康状态

#### 成本优化场景
1. 系统定期分析各通道的成本效益
2. 根据`cost_factor`和实际使用情况调整权重
3. 优先选择成本较低但质量稳定的通道
4. 在高峰期自动切换到备用通道分散负载

#### 故障恢复场景
1. 通道出现故障时自动标记为不健康
2. 健康检查定期尝试恢复连接
3. 恢复后自动重新加入通道池
4. 根据历史表现调整优先级

## 4.6 遗漏的重要表结构补充

### 4.6.1 用户基础信息表 (user_base_info)
```sql
CREATE TABLE `user_base_info` (
  `id` int AUTO_INCREMENT PRIMARY KEY,
  `commission_id` int COMMENT '分佣身份ID',
  `account` varchar(100) COMMENT '账号(手机号或其他账号)',
  `name` varchar(100) COMMENT '用户名',
  `nick_name` varchar(100) COMMENT '昵称',
  `password` varchar(255) COMMENT '密码(BCrypt加密)',
  `login_time` datetime COMMENT '最后登录时间',
  `ip` varchar(50) COMMENT 'IP地址',
  `head_sculpture` varchar(500) COMMENT '头像URL',
  `address` varchar(200) COMMENT '地址',
  `email` varchar(100) COMMENT '邮箱',
  `status` int(1) DEFAULT 0 COMMENT '状态(0:正常 1:禁用)',
  `use_num` int DEFAULT 0 COMMENT '剩余可用次数(充值)',
  `free_num` int DEFAULT 0 COMMENT '免费可用次数(赠送)',
  `daily_free_time` int DEFAULT 0 COMMENT '每日免费次数',
  `draw_num` int DEFAULT 0 COMMENT '绘画次数',
  `music_num` int DEFAULT 0 COMMENT '音乐创作次数',
  `write_num` int DEFAULT 0 COMMENT '写作次数',
  `vip_end_time` datetime COMMENT '会员到期时间',
  `invite_user_id` varchar(64) COMMENT '邀请人ID',
  `users_id` bigint COMMENT 'users表关联ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `remark` varchar(500) COMMENT '备注',
  UNIQUE KEY `uk_account` (`account`),
  KEY `idx_status` (`status`),
  KEY `idx_vip_end_time` (`vip_end_time`)
) COMMENT '用户基础信息表';
```

**业务逻辑说明:**
- 这是系统的主用户表，存储用户的核心信息
- `use_num`、`free_num`等字段用于次数控制和计费
- 支持与第三方用户表(`users`)的关联
- `commission_id`关联分佣身份，支持推广分佣功能

## 5. 完整数据表清单 (基于SQL文件分析)

### 5.1 核心业务表 (共61张表)

#### 5.1.1 用户相关表 (9张)
1. **user_base_info** - 用户基础信息表 (主用户表)
2. **users** - 第三方登录用户表 (Facebook/Google/浏览器指纹)
3. **wx_user_info** - 微信用户信息表
4. **app_sign** - APP用户签到表
5. **user_check_in_record** - 用户签到记录表
6. **user_points_log** - 用户积分记录表
7. **user_config** - 用户配置表
8. **user_merge_info** - 用户合并记录表
9. **commission_identity** - 分佣身份表 (推广分佣)

#### 5.1.2 聊天相关表 (3张)
1. **chat_room** - 聊天室表
2. **chat_message** - 聊天消息表
3. **chat_agent** - 智能体配置表

#### 5.1.3 AI模型通道表 (3张)
1. **channel_config** - 通道配置表 (模型与站点映射)
2. **site_info** - 站点信息表 (API站点配置)
3. **model** - 模型表 (AI模型定义)

#### 5.1.4 支付相关表 (7张)
1. **product** - 产品信息表
2. **al_orders** - 支付宝订单表
3. **se_pay_order** - SE支付订单表 (越南本地支付)
4. **wx_pay_order** - 微信支付订单表
5. **qr_payment** - 二维码支付记录表
6. **recharge_log** - 充值记录表
7. **transfer_info** - 提现申请信息表

#### 5.1.5 塔罗牌相关表 (4张)
1. **tarot_spread** - 塔罗牌阵表
2. **tarot_card_meaning** - 塔罗牌义表
3. **tarot_reading_record** - 塔罗解读记录表
4. **tarot_daily_insight** - 每日洞察表

#### 5.1.6 写作应用表 (3张)
1. **write_agent** - 写作应用表
2. **category_info** - 分类信息表
3. **prompt_info** - 提示词表

#### 5.1.7 绘画相关表 (2张)
1. **midjourney_task** - Midjourney任务表
2. **draw_config** - 绘画配置表

#### 5.1.8 系统管理表 (12张)
1. **sys_config** - 系统配置表
2. **sys_oper_log** - 操作日志表
3. **exception_log** - 异常日志表
4. **sys_dict_data** - 字典数据表
5. **sys_dict_type** - 字典类型表
6. **sys_job** - 定时任务表
7. **sys_job_log** - 定时任务日志表
8. **sys_logininfor** - 登录日志表
9. **sys_menu** - 菜单权限表
10. **sys_role** - 角色信息表
11. **sys_role_menu** - 角色菜单关联表
12. **sys_user** - 系统用户表

#### 5.1.9 微信集成表 (2张)
1. **wx_user_info** - 微信用户信息表
2. **wx_pay_order** - 微信支付订单表

#### 5.1.10 其他业务表 (19张)
1. **news_info** - 新闻信息表
2. **member_info** - 会员信息表
3. **midjourney_task** - Midjourney绘画任务表
4. **draw_config** - 绘画配置表
5. **sensitive_word** - 敏感词表
6. **user_sensitive_word** - 用户敏感词记录表
7. **api_key_pool** - API密钥池表
8. **model_provider** - 模型提供商表
9. **channel_usage_log** - 通道使用日志表
10. **cost_analysis** - 成本分析表
11. **rate_limit_log** - 限流日志表
12. **health_check_log** - 健康检查日志表
13. **user_feedback** - 用户反馈表
14. **system_announcement** - 系统公告表
15. **version_update** - 版本更新表
16. **email_log** - 邮件发送日志表
17. **sms_log** - 短信发送日志表
18. **file_storage** - 文件存储表
19. **backup_record** - 备份记录表

### 5.2 字段类型统计

#### 5.2.1 主键设计
- 所有表使用 `id` 作为主键
- 数据类型：`int(11) AUTO_INCREMENT` 或 `bigint AUTO_INCREMENT`
- 选择原则：预估数据量 < 1000万用int，否则用bigint

#### 5.2.2 时间字段设计
- `create_time` - 创建时间，默认 `CURRENT_TIMESTAMP`
- `update_time` - 更新时间，默认 `CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP`
- 特殊时间字段：`login_time`、`pay_time`、`vip_end_time` 等

#### 5.2.3 状态字段设计
- 通用状态：`status int(1) DEFAULT '0'`
  - 0: 正常/启用/有效
  - 1: 删除/禁用/无效
- 订单状态：`status int(1)`
  - 0: 待支付
  - 1: 已支付
  - 2: 已取消
  - 3: 已退款

#### 5.2.4 金额字段设计
- 数据类型：`decimal(10,2)`
- 精度：支持最大99,999,999.99
- 币种字段：`currency varchar(20) DEFAULT 'VND'`

#### 5.2.5 文本字段设计
- 短文本：`varchar(100)` - 用户名、标题等
- 中文本：`varchar(500)` - 描述、备注等
- 长文本：`text` - 内容、配置等
- 超长文本：`longtext` - 大量数据存储

### 5.3 索引设计详情

#### 5.3.1 主键索引
```sql
PRIMARY KEY (`id`)
```

#### 5.3.2 唯一索引
```sql
-- 用户账号唯一
UNIQUE KEY `uk_account` (`account`)
-- 订单号唯一
UNIQUE KEY `uk_order_no` (`order_no`)
-- 配置键唯一
UNIQUE KEY `uk_config_key` (`config_key`)
```

#### 5.3.3 普通索引
```sql
-- 用户ID索引
KEY `idx_user_id` (`user_id`)
-- 状态索引
KEY `idx_status` (`status`)
-- 时间索引
KEY `idx_create_time` (`create_time`)
-- 类型索引
KEY `idx_type` (`type`)
```

#### 5.3.4 复合索引
```sql
-- 用户+时间复合索引
KEY `idx_user_time` (`user_id`, `create_time`)
-- 状态+排序复合索引
KEY `idx_status_sort` (`status`, `sort`)
-- 时间范围索引
KEY `idx_time_range` (`start_time`, `end_time`)
```

### 5.4 数据库约束设计

#### 5.4.1 非空约束
- 主键字段：NOT NULL
- 必填业务字段：NOT NULL
- 可选字段：允许NULL，设置DEFAULT值

#### 5.4.2 默认值约束
```sql
-- 状态字段默认值
`status` int(1) DEFAULT '0'
-- 时间字段默认值
`create_time` datetime DEFAULT CURRENT_TIMESTAMP
-- 数值字段默认值
`use_num` int(11) DEFAULT '0'
-- 字符串字段默认值
`currency` varchar(20) DEFAULT 'VND'
```

#### 5.4.3 检查约束 (MySQL 8.0+)
```sql
-- 状态值检查
CONSTRAINT `chk_status` CHECK (`status` IN (0, 1))
-- 金额非负检查
CONSTRAINT `chk_amount` CHECK (`amount` >= 0)
-- 积分非负检查
CONSTRAINT `chk_points` CHECK (`points` >= 0)
```

### 5.5 数据库字符集和排序规则

#### 5.5.1 字符集选择
- **utf8mb4**: 支持完整的UTF-8字符集，包括emoji
- **优势**: 支持4字节Unicode字符，兼容性好
- **应用**: 所有表统一使用utf8mb4

#### 5.5.2 排序规则选择
- **utf8mb4_unicode_ci**: Unicode标准排序，支持多语言
- **优势**: 准确的多语言排序，适合国际化应用
- **应用**: 所有表统一使用utf8mb4_unicode_ci

#### 5.5.3 行格式选择
- **ROW_FORMAT=DYNAMIC**: 动态行格式
- **优势**: 支持变长字段优化，节省存储空间
- **应用**: 所有表统一使用DYNAMIC行格式

#### 5.5.4 建表语句标准格式
```sql
CREATE TABLE `example_table` (
  `id` int(11) unsigned AUTO_INCREMENT COMMENT '主键' PRIMARY KEY,
  `name` varchar(100) NOT NULL COMMENT '名称',
  `status` int(1) DEFAULT 0 NULL COMMENT '状态',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP() NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP() NULL ON UPDATE CURRENT_TIMESTAMP() COMMENT '更新时间'
) COMMENT '示例表' CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;
```

## 6. 数据库设计规范总结

### 6.1 命名规范
- **表名**: 小写字母+下划线，如 `chat_message`
- **字段名**: 小写字母+下划线，如 `create_time`
- **索引名**: `idx_` + 字段名，如 `idx_user_id`
- **唯一索引**: `uk_` + 字段名，如 `uk_account`

### 6.2 字段设计规范
- **主键**: 统一使用 `id` 作为主键，类型为 `int unsigned AUTO_INCREMENT`
- **时间字段**: 统一使用 `datetime` 类型，默认值 `CURRENT_TIMESTAMP()`
- **状态字段**: 统一使用 `int(1)` 类型，0表示正常/启用，1表示异常/禁用
- **金额字段**: 统一使用 `decimal(15,2)` 类型，支持大额交易
- **文本字段**: 根据长度选择 `varchar` 或 `text` 类型

### 6.3 索引设计规范
- **主键索引**: 每张表必须有主键
- **外键索引**: 所有外键字段建立索引
- **查询索引**: 根据业务查询需求建立合适索引
- **复合索引**: 多字段查询建立复合索引，注意字段顺序

### 6.4 注释规范
- **表注释**: 每张表必须有中文注释说明用途
- **字段注释**: 每个字段必须有中文注释说明含义
- **枚举值**: 状态字段注释中说明各值含义

### 6.5 性能优化建议
- **分区表**: 大数据量表考虑按时间或ID分区
- **读写分离**: 主从复制，读写分离提高性能
- **缓存策略**: 热点数据使用Redis缓存
- **归档策略**: 历史数据定期归档，保持表大小合理
