# 超级智能社(SuperAI)需求功能清单表格

## 1. 用户管理模块

| 功能编号 | 功能名称 | 功能描述 | 优先级 | 状态 | 接口路径 | 实现类/方法 | 数据表 | 备注 |
|---------|---------|---------|--------|------|----------|------------|--------|------|
| UM001 | 用户注册 | 支持手机号、邮箱注册 | 高 | 已实现 | /register | UserBaseInfoService.register() | user_base_info | 支持验证码验证，BCrypt密码加密 |
| UM002 | 用户登录 | 账号密码登录 | 高 | 已实现 | /login | LoginManager.login() | user_base_info | Sa-Token会话管理，支持记住登录 |
| UM003 | Facebook登录 | Facebook OAuth2登录 | 中 | 已实现 | /auth/facebook/callback | FacebookAuthService.login() | users | 自动创建或更新用户信息 |
| UM004 | Google登录 | Google OAuth2登录 | 中 | 已实现 | /auth/google/callback | GoogleAuthService.login() | users | 支持邮箱绑定和头像同步 |
| UM005 | 浏览器指纹登录 | 基于设备指纹的免密登录 | 中 | 已实现 | /auth/fingerprint | FingerprintByBrowserAuthService | users | 设备唯一标识，防刷机制 |
| UM006 | 微信小程序登录 | 微信小程序静默登录 | 高 | 已实现 | /wx/login | WxUserInfoService.login() | wx_user_info | 获取用户基本信息和头像 |
| UM007 | 用户信息查询 | 查看个人基本信息 | 高 | 已实现 | /user/info | UserBaseInfoService.queryUserInfo() | user_base_info | 包含积分、VIP状态等 |
| UM008 | 用户信息修改 | 修改昵称、头像等 | 高 | 已实现 | /user/update | UserBaseInfoService.updateUserInfo() | user_base_info | 支持头像上传，最大2MB |
| UM009 | 密码修改 | 修改登录密码 | 高 | 已实现 | /user/password | UserBaseInfoService.updatePassword() | user_base_info | 需要验证原密码 |
| UM010 | 账号注销 | 用户主动注销账号 | 低 | 待实现 | /user/delete | - | user_base_info | 需要数据清理和关联处理 |

## 2. 聊天功能模块

| 功能编号 | 功能名称 | 功能描述 | 优先级 | 状态 | 接口路径 | 实现类/方法 | 数据表 | 备注 |
|---------|---------|---------|--------|------|----------|------------|--------|------|
| CM001 | 创建聊天室 | 创建新的聊天会话 | 高 | 已实现 | /createChatRoom | ChatRoomService.createChatRoom() | chat_room | 支持智能体选择，微信OpenID集成 |
| CM002 | AI对话V2 | 流式AI对话功能 | 高 | 已实现 | /chat/v2/buildStreamingRequests | ChatMsgV2BuildHelper.buildMessageBody() | chat_message | SSE流式响应，支持GPT-3.5/4，记录Token消耗 |
| CM003 | AI对话V3 | 批量完成对话 | 高 | 已实现 | /chat/v3/buildBatchCompletion | ChatMsgBuildHelper.buildBatchCompletion() | chat_message | 用于塔罗解读等批量处理，支持多站点配置 |
| CM004 | 聊天历史查询 | 查看历史对话记录 | 高 | 已实现 | /queryChatMessage | ChatMessageService.queryChatMessage() | chat_message | 分页查询，支持OpenID和房间ID筛选 |
| CM005 | 删除聊天室 | 删除聊天会话 | 中 | 已实现 | /deleteRoom/{roomId} | ChatRoomService.deleteRoom() | chat_room | 软删除机制，保留历史消息 |
| CM006 | 聊天室列表 | 查询用户聊天室列表 | 高 | 已实现 | /getChatRoom | ChatRoomService.getChatRoom() | chat_room | 支持类型筛选(CHAT/MUSIC)，公开/私有筛选 |
| CM007 | 模型列表 | 获取可用AI模型 | 高 | 已实现 | /models | ChatController.models() | sys_config | 返回GPT-3.5/4等模型信息和配置 |
| CM008 | 图片生成 | AI绘画功能 | 中 | 已实现 | /chatImage | ChatController.chatImage() | chat_message | Midjourney集成，消耗绘画次数 |
| CM009 | 聊天转接 | 聊天会话转接 | 中 | 已实现 | /chat/transfer | ChatTransferController | chat_message | 支持会话转移和上下文保持 |
| CM010 | 文件上传 | 支持文件对话 | 中 | 已实现 | /upload | FileUploadController | file_upload_record | 最大30MB，支持PDF/DOC等格式 |

## 3. 智能体模块

| 功能编号 | 功能名称 | 功能描述 | 优先级 | 状态 | 接口路径 | 备注 |
|---------|---------|---------|--------|------|----------|------|
| IA001 | 智能体列表 | 分页查询智能体 | 高 | 已实现 | /intelligentAgent/page/list | 支持分类筛选 |
| IA002 | 智能体详情 | 查看智能体详细信息 | 高 | 已实现 | /intelligentAgent/getAgentByName/{name} | 包含提示词等 |
| IA003 | 智能体分类 | 查询分类列表 | 中 | 已实现 | /intelligentAgent/queryTagList | 动态分类 |
| IA004 | 默认智能体 | 获取默认智能体 | 高 | 已实现 | /intelligentAgent/getDefaultAgent | ZNS智能体 |
| IA005 | 智能体创建 | 用户创建自定义智能体 | 中 | 已实现 | /intelligentAgent/save | 需要权限验证 |
| IA006 | 使用统计 | 智能体使用次数统计 | 低 | 已实现 | - | 热度排序 |
| IA007 | 远程数据同步 | 从第三方同步智能体 | 低 | 已实现 | /intelligentAgent/saveDataRemotely | 管理员功能 |

## 4. 写作应用模块

| 功能编号 | 功能名称 | 功能描述 | 优先级 | 状态 | 接口路径 | 备注 |
|---------|---------|---------|--------|------|----------|------|
| WA001 | 写作应用列表 | 分页查询写作应用 | 高 | 已实现 | /writeAgent/page/list | 支持分类筛选 |
| WA002 | 写作分类 | 查询写作应用分类 | 中 | 已实现 | /writeAgent/queryTagList | 动态分类 |
| WA003 | 写作应用详情 | 查看应用详细信息 | 高 | 已实现 | - | 包含模板等 |

## 5. 塔罗牌模块

| 功能编号 | 功能名称 | 功能描述 | 优先级 | 状态 | 接口路径 | 实现类/方法 | 数据表 | 备注 |
|---------|---------|---------|--------|------|----------|------------|--------|------|
| TR001 | 塔罗牌阵列表 | 查询可用牌阵 | 高 | 已实现 | /tarot/spread/list | TarotSpreadService.list() | tarot_spread | 包含消耗积分信息，支持状态筛选 |
| TR002 | 随机抽牌 | 随机生成塔罗牌 | 高 | 已实现 | /tarot/spread/randomTarot | TarotCardMeaningService.randomTarot() | tarot_card_meaning | Fisher-Yates洗牌算法，正逆位随机 |
| TR003 | 塔罗解读 | AI解读塔罗牌 | 高 | 已实现 | /chat/v3/buildBatchCompletion | TarotReadingService.buildBatchCompletion() | tarot_reading_record | GPT-4专业解读，支持多种牌阵 |
| TR004 | 解读历史 | 查看历史解读记录 | 中 | 已实现 | /tarot/readingRecord/list | TarotReadingRecordService.list() | tarot_reading_record | 分页查询，支持软删除 |
| TR005 | 每日洞察 | 每日塔罗洞察 | 中 | 已实现 | /tarot/dailyInsight/today | TarotDailyInsightService.getTodayInsight() | tarot_daily_insight | 每日更新，包含幸运色和幸运数字 |
| TR006 | 牌阵详情 | 查看牌阵详细信息 | 中 | 已实现 | /tarot/spread/{id} | TarotSpreadService.getById() | tarot_spread | 包含布局图、示例、消耗等详细信息 |
| TR007 | 牌义查询 | 查询塔罗牌含义 | 中 | 已实现 | /tarot/cardMeaning/list | TarotCardMeaningService.list() | tarot_card_meaning | 支持分类筛选，包含正逆位含义 |

## 6. 支付模块

| 功能编号 | 功能名称 | 功能描述 | 优先级 | 状态 | 接口路径 | 实现类/方法 | 数据表 | 备注 |
|---------|---------|---------|--------|------|----------|------------|--------|------|
| PM001 | 产品列表 | 查询可购买产品 | 高 | 已实现 | /pay/products | ProductService.list() | product | 支持类型筛选，多币种价格 |
| PM002 | 微信支付 | 微信支付下单 | 高 | 已实现 | /pay/wx/create | WxPayService.createOrder() | wx_pay_order | 支持多商户，生成支付二维码 |
| PM003 | 支付宝支付 | 支付宝支付下单 | 高 | 已实现 | /pay/ali/create | AlipayService.createOrder() | al_orders | 扫码支付，15分钟过期 |
| PM004 | SE支付 | 越南本地支付下单 | 高 | 已实现 | /sepay/create | SePayService.createOrder() | se_pay_order | 银行转账，生成QR码 |
| PM005 | 支付回调 | 处理支付结果通知 | 高 | 已实现 | /notify/{channel} | PayNotifyService.handleNotify() | 各支付订单表 | 异步处理，防重复 |
| PM006 | 订单查询 | 查询订单状态 | 高 | 已实现 | /pay/status/{orderNo} | OrderService.getStatus() | 各支付订单表 | 实时状态查询 |
| PM007 | 充值记录 | 查询充值历史记录 | 中 | 已实现 | /pay/records | PayRecordService.list() | al_orders等 | 分页查询，支持时间筛选 |
| PM008 | 提现申请 | 用户申请提现 | 中 | 已实现 | /pay/withdraw | WithdrawService.apply() | withdraw_record | 需要审核，支持多种提现方式 |
| PM009 | Momo支付 | 越南Momo支付 | 中 | 已实现 | /momo/create | MomoPayService.createOrder() | momo_pay_order | 越南本地钱包支付 |

## 7. 积分系统模块

| 功能编号 | 功能名称 | 功能描述 | 优先级 | 状态 | 接口路径 | 备注 |
|---------|---------|---------|--------|------|----------|------|
| PS001 | 积分查询 | 查看当前积分余额 | 高 | 已实现 | /user/points | 实时余额 |
| PS002 | 积分消耗 | 使用功能消耗积分 | 高 | 已实现 | - | 自动扣减 |
| PS003 | 积分充值 | 购买积分 | 高 | 已实现 | - | 通过支付模块 |
| PS004 | 积分记录 | 查看积分变动记录 | 中 | 已实现 | /user/pointsLog | 分页查询 |
| PS005 | 积分赠送 | 系统赠送积分 | 中 | 已实现 | - | 签到、邀请等 |
| PS006 | 积分过期 | 积分有效期管理 | 低 | 待实现 | - | 定时任务 |

## 8. 签到模块

| 功能编号 | 功能名称 | 功能描述 | 优先级 | 状态 | 接口路径 | 备注 |
|---------|---------|---------|--------|------|----------|------|
| SI001 | 每日签到 | 用户每日签到 | 高 | 已实现 | /tarot/checkInRecord/checkIn | 分布式锁 |
| SI002 | 签到记录 | 查看签到历史 | 中 | 已实现 | /tarot/checkInRecord/list | 近期记录 |
| SI003 | 签到奖励 | 签到获得积分 | 高 | 已实现 | - | 可配置奖励 |
| SI004 | 连续签到 | 连续签到额外奖励 | 中 | 已实现 | - | 递增奖励 |
| SI005 | 签到状态 | 查询今日签到状态 | 高 | 已实现 | - | 防重复签到 |

## 9. 配置管理模块

| 功能编号 | 功能名称 | 功能描述 | 优先级 | 状态 | 接口路径 | 备注 |
|---------|---------|---------|--------|------|----------|------|
| CF001 | 系统配置 | 查询系统配置 | 高 | 已实现 | /sysConfig | 开关、限制等 |
| CF002 | 首页配置 | 首页内容配置 | 中 | 已实现 | /config | 动态配置 |
| CF003 | 分类配置 | 功能分类配置 | 中 | 已实现 | /category/list | 分类管理 |
| CF004 | 会员配置 | 会员等级配置 | 中 | 已实现 | /memberList | 等级权益 |
| CF005 | 提词器配置 | 提示词配置 | 低 | 已实现 | /prompt | 随机提示 |
| CF006 | 新闻配置 | 首页新闻配置 | 低 | 已实现 | /queryNews | 随机展示 |

## 10. 系统管理模块

| 功能编号 | 功能名称 | 功能描述 | 优先级 | 状态 | 接口路径 | 备注 |
|---------|---------|---------|--------|------|----------|------|
| SM001 | 操作日志 | 记录用户操作 | 中 | 已实现 | - | AOP切面 |
| SM002 | 异常日志 | 记录系统异常 | 高 | 已实现 | - | 全局异常处理 |
| SM003 | 定时任务 | 系统定时任务 | 中 | 已实现 | /Job/payouts | 提现处理等 |
| SM004 | 数据统计 | 业务数据统计 | 低 | 待实现 | - | 报表功能 |
| SM005 | 系统监控 | 系统运行监控 | 中 | 待实现 | - | 健康检查 |

## 11. 微信集成模块

| 功能编号 | 功能名称 | 功能描述 | 优先级 | 状态 | 接口路径 | 备注 |
|---------|---------|---------|--------|------|----------|------|
| WX001 | 微信登录 | 微信小程序登录 | 高 | 已实现 | - | 静默登录 |
| WX002 | 微信支付 | 微信支付集成 | 高 | 已实现 | - | 小程序支付 |
| WX003 | 微信消息 | 微信消息处理 | 中 | 已实现 | - | 消息回复 |
| WX004 | 用户信息 | 获取微信用户信息 | 高 | 已实现 | - | 头像昵称等 |

## 12. AI模型通道管理模块

| 功能编号 | 功能名称 | 功能描述 | 优先级 | 状态 | 接口路径 | 实现类/方法 | 数据表 | 备注 |
|---------|---------|---------|--------|------|----------|------------|--------|------|
| MC001 | 模型列表管理 | 管理可用AI模型 | 高 | 已实现 | /api/model/list | ModelService.list() | model | 支持多提供商模型配置 |
| MC002 | 通道配置管理 | 配置模型与站点映射 | 高 | 已实现 | /api/channel/config | ChannelConfigService.config() | channel_config | 支持权重和优先级设置 |
| MC003 | 站点信息管理 | 管理API站点配置 | 高 | 已实现 | /api/site/manage | SiteInfoService.manage() | site_info | 包含API密钥和限流配置 |
| MC004 | 最优通道选择 | 智能选择最优通道 | 高 | 已实现 | - | ChannelSelector.selectOptimal() | 三表联查 | 基于成本、性能、可用性选择 |
| MC005 | 故障转移机制 | 通道故障自动切换 | 高 | 已实现 | - | ChannelFailover.switchChannel() | channel_config | 支持多级故障转移 |
| MC006 | 健康检查 | 定期检查通道健康状态 | 中 | 已实现 | /api/channel/health | HealthCheckService.check() | channel_config | 自动禁用异常通道 |
| MC007 | 使用统计 | 统计通道使用情况 | 中 | 已实现 | /api/channel/stats | ChannelStatsService.stats() | channel_usage_log | 包含成本和性能分析 |
| MC008 | 负载均衡 | 通道负载均衡 | 中 | 已实现 | - | LoadBalancer.balance() | channel_config | 基于权重的负载分配 |

## 13. 绘画功能模块

| 功能编号 | 功能名称 | 功能描述 | 优先级 | 状态 | 接口路径 | 实现类/方法 | 数据表 | 备注 |
|---------|---------|---------|--------|------|----------|------------|--------|------|
| DR001 | Midjourney绘画 | AI图像生成 | 高 | 已实现 | /api/draw/midjourney | MidjourneyService.generate() | midjourney_task | Discord Bot集成 |
| DR002 | 任务状态查询 | 查询绘画任务状态 | 高 | 已实现 | /api/draw/task/{taskId} | MidjourneyService.queryTask() | midjourney_task | 实时状态更新 |
| DR003 | 图片放大 | 图片质量提升 | 中 | 已实现 | /api/draw/upscale | MidjourneyService.upscale() | midjourney_task | 4倍放大功能 |
| DR004 | 图片变换 | 基于原图生成变体 | 中 | 已实现 | /api/draw/variation | MidjourneyService.variation() | midjourney_task | 生成4个变体 |
| DR005 | 绘画历史 | 查看绘画历史记录 | 中 | 已实现 | /api/draw/history | MidjourneyService.getHistory() | midjourney_task | 分页查询，支持筛选 |
| DR006 | 提示词优化 | 自动优化绘画提示词 | 低 | 已实现 | /api/draw/optimize-prompt | PromptOptimizer.optimize() | - | AI辅助提示词生成 |

## 14. 内容审核模块

| 功能编号 | 功能名称 | 功能描述 | 优先级 | 状态 | 接口路径 | 实现类/方法 | 数据表 | 备注 |
|---------|---------|---------|--------|------|----------|------------|--------|------|
| CM001 | 敏感词检测 | 检测和过滤敏感内容 | 高 | 已实现 | - | SensitiveWordFilter.filter() | sensitive_word | 支持多语言和正则匹配 |
| CM002 | 内容替换 | 自动替换敏感词 | 高 | 已实现 | - | ContentReplacer.replace() | sensitive_word | 支持自定义替换词 |
| CM003 | 审核日志 | 记录审核结果 | 中 | 已实现 | /api/audit/log | AuditLogService.log() | user_sensitive_word | 用户违规记录 |
| CM004 | 敏感词管理 | 管理敏感词库 | 中 | 已实现 | /api/sensitive/manage | SensitiveWordService.manage() | sensitive_word | 支持批量导入导出 |

## 功能统计

| 模块 | 总功能数 | 已实现 | 待实现 | 实现率 |
|------|---------|--------|--------|--------|
| 用户管理 | 6 | 5 | 1 | 83.3% |
| 聊天功能 | 8 | 8 | 0 | 100% |
| 智能体 | 7 | 7 | 0 | 100% |
| 写作应用 | 3 | 3 | 0 | 100% |
| 塔罗牌 | 6 | 6 | 0 | 100% |
| 支付模块 | 8 | 8 | 0 | 100% |
| 积分系统 | 6 | 5 | 1 | 83.3% |
| 签到模块 | 5 | 5 | 0 | 100% |
| 配置管理 | 6 | 6 | 0 | 100% |
| 系统管理 | 5 | 3 | 2 | 60% |
| 微信集成 | 4 | 4 | 0 | 100% |
| AI模型通道 | 8 | 8 | 0 | 100% |
| 绘画功能 | 6 | 6 | 0 | 100% |
| 内容审核 | 4 | 4 | 0 | 100% |
| **总计** | **86** | **82** | **4** | **95.3%** |
