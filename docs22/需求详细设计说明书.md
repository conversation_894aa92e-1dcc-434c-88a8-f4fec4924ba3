# 超级智能社(SuperAI)重构版需求详细设计说明书

## 1. 用户模块详细设计

### 1.1 包结构设计

```
com.hncboy.aiend.module.user
├── controller
│   ├── UserController.java                    # 用户管理控制器
│   ├── UserAuthController.java                # 用户认证控制器
│   └── UserProfileController.java             # 用户资料控制器
├── service
│   ├── UserService.java                       # 用户服务接口
│   ├── UserAuthService.java                   # 用户认证服务接口
│   ├── UserJointLoginService.java             # 联合登录服务接口
│   └── impl
│       ├── UserServiceImpl.java               # 用户服务实现
│       ├── UserAuthServiceImpl.java           # 用户认证服务实现
│       └── UserJointLoginServiceImpl.java     # 联合登录服务实现
├── mapper
│   ├── UserMapper.java                        # 用户数据访问
│   ├── UserJointLoginMapper.java              # 联合登录数据访问
│   └── UserJointConfigMapper.java             # 联合登录配置数据访问
├── domain
│   ├── entity
│   │   ├── User.java                          # 用户实体
│   │   ├── UserJointLogin.java                # 联合登录实体
│   │   └── UserJointConfig.java               # 联合登录配置实体
│   ├── dto
│   │   ├── UserRegisterDTO.java               # 用户注册DTO
│   │   ├── UserLoginDTO.java                  # 用户登录DTO
│   │   ├── UserUpdateDTO.java                 # 用户更新DTO
│   │   └── UserDeactivateDTO.java             # 用户注销DTO
│   └── vo
│       ├── UserInfoVO.java                    # 用户信息VO
│       ├── UserProfileVO.java                 # 用户资料VO
│       └── LoginResultVO.java                 # 登录结果VO
└── auth
    ├── provider
    │   ├── AbstractAuthProvider.java          # 抽象认证提供者
    │   ├── WechatAuthProvider.java            # 微信认证提供者
    │   ├── GoogleAuthProvider.java            # Google认证提供者
    │   ├── FacebookAuthProvider.java          # Facebook认证提供者
    │   ├── PhoneAuthProvider.java             # 手机号认证提供者
    │   ├── EmailAuthProvider.java             # 邮箱认证提供者
    │   ├── PasswordAuthProvider.java          # 密码认证提供者
    │   └── FingerprintAuthProvider.java       # 指纹认证提供者
    ├── handler
    │   ├── AuthSuccessHandler.java            # 认证成功处理器
    │   ├── AuthFailureHandler.java            # 认证失败处理器
    │   └── AuthCallbackHandler.java           # 认证回调处理器
    └── config
        ├── JustAuthConfig.java                # JustAuth配置
        └── AuthProviderConfig.java           # 认证提供者配置
```

### 1.2 核心类设计

#### 1.2.1 用户实体类 (User.java)
```java
@Data
@TableName("user")
@ApiModel("用户实体")
public class User {
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty("用户ID")
    private Long id;
    
    @ApiModelProperty("用户名")
    private String username;
    
    @ApiModelProperty("昵称")
    private String nickname;
    
    @ApiModelProperty("邮箱")
    private String email;
    
    @ApiModelProperty("手机号")
    private String phone;
    
    @ApiModelProperty("头像URL")
    private String avatar;
    
    @ApiModelProperty("性别(0:未知 1:男 2:女)")
    private Integer gender;
    
    @ApiModelProperty("生日")
    private LocalDate birthday;
    
    @ApiModelProperty("国家代码")
    private String countryCode;
    
    @ApiModelProperty("语言偏好")
    private String language;
    
    @ApiModelProperty("时区")
    private String timezone;
    
    @ApiModelProperty("币种偏好")
    private String currency;
    
    @ApiModelProperty("用户状态(0:正常 1:禁用 2:注销)")
    private Integer status;
    
    @ApiModelProperty("注册渠道")
    private String registerChannel;
    
    @ApiModelProperty("注册IP")
    private String registerIp;
    
    @ApiModelProperty("最后登录时间")
    private LocalDateTime lastLoginTime;
    
    @ApiModelProperty("最后登录IP")
    private String lastLoginIp;
    
    @ApiModelProperty("积分余额")
    private Integer points;
    
    @ApiModelProperty("VIP到期时间")
    private LocalDateTime vipExpireTime;
    
    @ApiModelProperty("是否删除(0:否 1:是)")
    @TableLogic
    private Integer deleted;
    
    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}
```

#### 1.2.2 联合登录实体类 (UserJointLogin.java)
```java
@Data
@TableName("user_joint_login")
@ApiModel("用户联合登录实体")
public class UserJointLogin {
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty("主键ID")
    private Long id;
    
    @ApiModelProperty("用户ID")
    private Long userId;
    
    @ApiModelProperty("登录类型(WECHAT/GOOGLE/FACEBOOK/PHONE/EMAIL/PASSWORD/FINGERPRINT)")
    private String loginType;
    
    @ApiModelProperty("第三方唯一标识")
    private String thirdPartyId;
    
    @ApiModelProperty("第三方用户名")
    private String thirdPartyUsername;
    
    @ApiModelProperty("第三方邮箱")
    private String thirdPartyEmail;
    
    @ApiModelProperty("第三方头像")
    private String thirdPartyAvatar;
    
    @ApiModelProperty("访问令牌")
    private String accessToken;
    
    @ApiModelProperty("刷新令牌")
    private String refreshToken;
    
    @ApiModelProperty("令牌过期时间")
    private LocalDateTime tokenExpireTime;
    
    @ApiModelProperty("扩展信息(JSON格式)")
    private String extraInfo;
    
    @ApiModelProperty("是否主要登录方式(0:否 1:是)")
    private Integer isPrimary;
    
    @ApiModelProperty("状态(0:正常 1:禁用)")
    private Integer status;
    
    @ApiModelProperty("绑定时间")
    private LocalDateTime bindTime;
    
    @ApiModelProperty("最后使用时间")
    private LocalDateTime lastUseTime;
    
    @ApiModelProperty("是否删除(0:否 1:是)")
    @TableLogic
    private Integer deleted;
    
    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}
```

#### 1.2.3 联合登录配置实体类 (UserJointConfig.java)
```java
@Data
@TableName("user_joint_config")
@ApiModel("用户联合登录配置实体")
public class UserJointConfig {
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty("主键ID")
    private Long id;
    
    @ApiModelProperty("登录类型")
    private String loginType;
    
    @ApiModelProperty("配置名称")
    private String configName;
    
    @ApiModelProperty("客户端ID")
    private String clientId;
    
    @ApiModelProperty("客户端密钥")
    private String clientSecret;
    
    @ApiModelProperty("回调地址")
    private String redirectUri;
    
    @ApiModelProperty("授权范围")
    private String scope;
    
    @ApiModelProperty("配置参数(JSON格式)")
    private String configParams;
    
    @ApiModelProperty("是否启用(0:否 1:是)")
    private Integer enabled;
    
    @ApiModelProperty("排序权重")
    private Integer sortOrder;
    
    @ApiModelProperty("适用环境(dev/test/prod)")
    private String environment;
    
    @ApiModelProperty("备注")
    private String remark;
    
    @ApiModelProperty("是否删除(0:否 1:是)")
    @TableLogic
    private Integer deleted;
    
    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}
```

### 1.3 核心服务接口设计

#### 1.3.1 用户服务接口 (UserService.java)
```java
public interface UserService extends IService<User> {
    
    /**
     * 根据用户ID获取用户信息
     */
    UserInfoVO getUserInfo(Long userId);
    
    /**
     * 更新用户资料
     */
    Boolean updateUserProfile(Long userId, UserUpdateDTO updateDTO);
    
    /**
     * 用户注销
     */
    Boolean deactivateUser(Long userId, UserDeactivateDTO deactivateDTO);
    
    /**
     * 检查用户是否存在
     */
    Boolean checkUserExists(String identifier, String identifierType);
    
    /**
     * 根据第三方信息查找用户
     */
    User findUserByThirdParty(String loginType, String thirdPartyId);
    
    /**
     * 创建新用户
     */
    User createUser(UserRegisterDTO registerDTO);
    
    /**
     * 更新用户最后登录信息
     */
    void updateLastLoginInfo(Long userId, String loginIp);
    
    /**
     * 获取用户统计信息
     */
    Map<String, Object> getUserStats(Long userId);
}
```

#### 1.3.2 用户认证服务接口 (UserAuthService.java)
```java
public interface UserAuthService {
    
    /**
     * 用户登录
     */
    LoginResultVO login(UserLoginDTO loginDTO);
    
    /**
     * 第三方登录
     */
    LoginResultVO thirdPartyLogin(String loginType, String authCode, String state);
    
    /**
     * 用户注册
     */
    LoginResultVO register(UserRegisterDTO registerDTO);
    
    /**
     * 发送验证码
     */
    Boolean sendVerificationCode(String target, String type);
    
    /**
     * 验证验证码
     */
    Boolean verifyCode(String target, String code, String type);
    
    /**
     * 刷新令牌
     */
    LoginResultVO refreshToken(String refreshToken);
    
    /**
     * 用户登出
     */
    Boolean logout(Long userId);
    
    /**
     * 绑定第三方账号
     */
    Boolean bindThirdPartyAccount(Long userId, String loginType, String authCode);
    
    /**
     * 解绑第三方账号
     */
    Boolean unbindThirdPartyAccount(Long userId, String loginType);
}
```

### 1.4 控制器接口设计

#### 1.4.1 用户认证控制器 (UserAuthController.java)
```java
@RestController
@RequestMapping("/api/v1/auth")
@Api(tags = "用户认证接口")
@Slf4j
public class UserAuthController {
    
    @PostMapping("/login")
    @ApiOperation("用户登录")
    public R<LoginResultVO> login(@Valid @RequestBody UserLoginDTO loginDTO);
    
    @PostMapping("/register")
    @ApiOperation("用户注册")
    public R<LoginResultVO> register(@Valid @RequestBody UserRegisterDTO registerDTO);
    
    @GetMapping("/oauth/{loginType}")
    @ApiOperation("第三方登录授权")
    public R<String> oauthAuthorize(@PathVariable String loginType);
    
    @PostMapping("/oauth/callback/{loginType}")
    @ApiOperation("第三方登录回调")
    public R<LoginResultVO> oauthCallback(@PathVariable String loginType, 
                                         @RequestParam String code,
                                         @RequestParam(required = false) String state);
    
    @PostMapping("/send-code")
    @ApiOperation("发送验证码")
    public R<Boolean> sendVerificationCode(@Valid @RequestBody SendCodeDTO sendCodeDTO);
    
    @PostMapping("/verify-code")
    @ApiOperation("验证验证码")
    public R<Boolean> verifyCode(@Valid @RequestBody VerifyCodeDTO verifyCodeDTO);
    
    @PostMapping("/refresh-token")
    @ApiOperation("刷新令牌")
    public R<LoginResultVO> refreshToken(@Valid @RequestBody RefreshTokenDTO refreshTokenDTO);
    
    @PostMapping("/logout")
    @ApiOperation("用户登出")
    @SaCheckLogin
    public R<Boolean> logout();
    
    @GetMapping("/login-configs")
    @ApiOperation("获取登录配置")
    public R<List<LoginConfigVO>> getLoginConfigs();
}
```

#### 1.4.2 用户管理控制器 (UserController.java)
```java
@RestController
@RequestMapping("/api/v1/user")
@Api(tags = "用户管理接口")
@SaCheckLogin
@Slf4j
public class UserController {
    
    @GetMapping("/info")
    @ApiOperation("获取用户信息")
    public R<UserInfoVO> getUserInfo();
    
    @PutMapping("/profile")
    @ApiOperation("更新用户资料")
    public R<Boolean> updateProfile(@Valid @RequestBody UserUpdateDTO updateDTO);
    
    @PostMapping("/deactivate")
    @ApiOperation("用户注销")
    public R<Boolean> deactivateAccount(@Valid @RequestBody UserDeactivateDTO deactivateDTO);
    
    @GetMapping("/joint-logins")
    @ApiOperation("获取绑定的第三方账号")
    public R<List<UserJointLoginVO>> getJointLogins();
    
    @PostMapping("/bind-account")
    @ApiOperation("绑定第三方账号")
    public R<Boolean> bindAccount(@Valid @RequestBody BindAccountDTO bindAccountDTO);
    
    @DeleteMapping("/unbind-account/{loginType}")
    @ApiOperation("解绑第三方账号")
    public R<Boolean> unbindAccount(@PathVariable String loginType);
    
    @GetMapping("/stats")
    @ApiOperation("获取用户统计信息")
    public R<Map<String, Object>> getUserStats();
    
    @PostMapping("/change-password")
    @ApiOperation("修改密码")
    public R<Boolean> changePassword(@Valid @RequestBody ChangePasswordDTO changePasswordDTO);
}
```

## 2. 支付模块详细设计

### 2.1 包结构设计

```
com.hncboy.aiend.module.payment
├── controller
│   ├── PaymentController.java                 # 支付控制器
│   ├── PaymentCallbackController.java         # 支付回调控制器
│   └── PaymentConfigController.java           # 支付配置控制器
├── service
│   ├── PaymentService.java                    # 支付服务接口
│   ├── PaymentOrderService.java               # 支付订单服务接口
│   ├── PaymentChannelService.java             # 支付渠道服务接口
│   └── impl
│       ├── PaymentServiceImpl.java            # 支付服务实现
│       ├── PaymentOrderServiceImpl.java       # 支付订单服务实现
│       └── PaymentChannelServiceImpl.java     # 支付渠道服务实现
├── mapper
│   ├── PaymentOrderMapper.java                # 支付订单数据访问
│   ├── PaymentChannelConfigMapper.java        # 支付渠道配置数据访问
│   └── ProductMapper.java                     # 产品数据访问
├── domain
│   ├── entity
│   │   ├── PaymentOrder.java                  # 支付订单实体
│   │   ├── PaymentChannelConfig.java          # 支付渠道配置实体
│   │   └── Product.java                       # 产品实体
│   ├── dto
│   │   ├── CreateOrderDTO.java                # 创建订单DTO
│   │   ├── PaymentCallbackDTO.java            # 支付回调DTO
│   │   └── PaymentQueryDTO.java               # 支付查询DTO
│   └── vo
│       ├── PaymentOrderVO.java                # 支付订单VO
│       ├── PaymentResultVO.java               # 支付结果VO
│       └── PaymentChannelVO.java              # 支付渠道VO
└── channel
    ├── AbstractPaymentChannel.java            # 抽象支付渠道
    ├── alipay
    │   ├── AlipayChannel.java                 # 支付宝支付渠道
    │   ├── AlipayConfig.java                  # 支付宝配置
    │   └── AlipayCallbackHandler.java         # 支付宝回调处理
    ├── wechat
    │   ├── WechatPayChannel.java              # 微信支付渠道
    │   ├── WechatPayConfig.java               # 微信支付配置
    │   └── WechatPayCallbackHandler.java      # 微信支付回调处理
    ├── momo
    │   ├── MomoPayChannel.java                # Momo支付渠道
    │   ├── MomoPayConfig.java                 # Momo支付配置
    │   └── MomoPayCallbackHandler.java        # Momo支付回调处理
    └── sepay
        ├── SePayChannel.java                  # SE支付渠道
        ├── SePayConfig.java                   # SE支付配置
        └── SePayCallbackHandler.java          # SE支付回调处理
```

### 2.2 核心类设计

#### 2.2.1 支付订单实体类 (PaymentOrder.java)
```java
@Data
@TableName("payment_order")
@ApiModel("支付订单实体")
public class PaymentOrder {
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty("订单ID")
    private Long id;
    
    @ApiModelProperty("订单号")
    private String orderNo;
    
    @ApiModelProperty("用户ID")
    private Long userId;
    
    @ApiModelProperty("产品ID")
    private Long productId;
    
    @ApiModelProperty("产品名称")
    private String productName;
    
    @ApiModelProperty("产品类型")
    private String productType;
    
    @ApiModelProperty("支付渠道")
    private String paymentChannel;
    
    @ApiModelProperty("支付方式")
    private String paymentMethod;
    
    @ApiModelProperty("订单金额")
    private BigDecimal amount;
    
    @ApiModelProperty("实付金额")
    private BigDecimal paidAmount;
    
    @ApiModelProperty("币种")
    private String currency;
    
    @ApiModelProperty("汇率")
    private BigDecimal exchangeRate;
    
    @ApiModelProperty("订单状态(0:待支付 1:已支付 2:已取消 3:已退款)")
    private Integer status;
    
    @ApiModelProperty("第三方订单号")
    private String thirdPartyOrderNo;
    
    @ApiModelProperty("第三方交易号")
    private String thirdPartyTransactionId;
    
    @ApiModelProperty("支付时间")
    private LocalDateTime payTime;
    
    @ApiModelProperty("过期时间")
    private LocalDateTime expireTime;
    
    @ApiModelProperty("回调时间")
    private LocalDateTime callbackTime;
    
    @ApiModelProperty("支付IP")
    private String paymentIp;
    
    @ApiModelProperty("支付参数(JSON格式)")
    private String paymentParams;
    
    @ApiModelProperty("回调参数(JSON格式)")
    private String callbackParams;
    
    @ApiModelProperty("失败原因")
    private String failureReason;
    
    @ApiModelProperty("备注")
    private String remark;
    
    @ApiModelProperty("是否删除(0:否 1:是)")
    @TableLogic
    private Integer deleted;
    
    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}
```

#### 2.2.2 支付渠道配置实体类 (PaymentChannelConfig.java)
```java
@Data
@TableName("payment_channel_config")
@ApiModel("支付渠道配置实体")
public class PaymentChannelConfig {
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty("主键ID")
    private Long id;
    
    @ApiModelProperty("渠道代码")
    private String channelCode;
    
    @ApiModelProperty("渠道名称")
    private String channelName;
    
    @ApiModelProperty("支付方式")
    private String paymentMethod;
    
    @ApiModelProperty("商户号")
    private String merchantId;
    
    @ApiModelProperty("应用ID")
    private String appId;
    
    @ApiModelProperty("应用密钥")
    private String appSecret;
    
    @ApiModelProperty("公钥")
    private String publicKey;
    
    @ApiModelProperty("私钥")
    private String privateKey;
    
    @ApiModelProperty("API地址")
    private String apiUrl;
    
    @ApiModelProperty("回调地址")
    private String notifyUrl;
    
    @ApiModelProperty("返回地址")
    private String returnUrl;
    
    @ApiModelProperty("支持币种(JSON数组)")
    private String supportedCurrencies;
    
    @ApiModelProperty("配置参数(JSON格式)")
    private String configParams;
    
    @ApiModelProperty("是否启用(0:否 1:是)")
    private Integer enabled;
    
    @ApiModelProperty("排序权重")
    private Integer sortOrder;
    
    @ApiModelProperty("适用环境(dev/test/prod)")
    private String environment;
    
    @ApiModelProperty("费率")
    private BigDecimal feeRate;
    
    @ApiModelProperty("最小金额")
    private BigDecimal minAmount;
    
    @ApiModelProperty("最大金额")
    private BigDecimal maxAmount;
    
    @ApiModelProperty("备注")
    private String remark;
    
    @ApiModelProperty("是否删除(0:否 1:是)")
    @TableLogic
    private Integer deleted;
    
    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}
```

### 2.3 核心服务接口设计

#### 2.3.1 支付服务接口 (PaymentService.java)
```java
public interface PaymentService {
    
    /**
     * 创建支付订单
     */
    PaymentResultVO createPaymentOrder(CreateOrderDTO createOrderDTO);
    
    /**
     * 查询支付订单
     */
    PaymentOrderVO queryPaymentOrder(String orderNo);
    
    /**
     * 处理支付回调
     */
    Boolean handlePaymentCallback(String channelCode, Map<String, Object> callbackData);
    
    /**
     * 取消支付订单
     */
    Boolean cancelPaymentOrder(String orderNo);
    
    /**
     * 申请退款
     */
    Boolean refundPaymentOrder(String orderNo, BigDecimal refundAmount, String reason);
    
    /**
     * 获取支付渠道列表
     */
    List<PaymentChannelVO> getPaymentChannels(String currency);
    
    /**
     * 同步支付状态
     */
    Boolean syncPaymentStatus(String orderNo);
    
    /**
     * 获取支付统计
     */
    Map<String, Object> getPaymentStats(PaymentQueryDTO queryDTO);
}
```

### 2.4 抽象支付渠道设计

#### 2.4.1 抽象支付渠道 (AbstractPaymentChannel.java)
```java
public abstract class AbstractPaymentChannel {
    
    /**
     * 渠道代码
     */
    public abstract String getChannelCode();
    
    /**
     * 渠道名称
     */
    public abstract String getChannelName();
    
    /**
     * 创建支付订单
     */
    public abstract PaymentResultVO createOrder(PaymentOrder order, PaymentChannelConfig config);
    
    /**
     * 查询支付状态
     */
    public abstract PaymentStatusVO queryStatus(String orderNo, PaymentChannelConfig config);
    
    /**
     * 处理支付回调
     */
    public abstract Boolean handleCallback(Map<String, Object> callbackData, PaymentChannelConfig config);
    
    /**
     * 申请退款
     */
    public abstract Boolean refund(String orderNo, BigDecimal amount, PaymentChannelConfig config);
    
    /**
     * 验证回调签名
     */
    public abstract Boolean verifyCallback(Map<String, Object> callbackData, PaymentChannelConfig config);
    
    /**
     * 获取支持的币种
     */
    public abstract List<String> getSupportedCurrencies();
    
    /**
     * 获取支持的支付方式
     */
    public abstract List<String> getSupportedMethods();
}
```

## 3. API接口设计规范

### 3.1 接口命名规范

#### 3.1.1 用户模块API
- `POST /api/v1/auth/login` - 用户登录
- `POST /api/v1/auth/register` - 用户注册
- `GET /api/v1/auth/oauth/{loginType}` - 第三方登录授权
- `POST /api/v1/auth/oauth/callback/{loginType}` - 第三方登录回调
- `POST /api/v1/auth/send-code` - 发送验证码
- `POST /api/v1/auth/verify-code` - 验证验证码
- `POST /api/v1/auth/refresh-token` - 刷新令牌
- `POST /api/v1/auth/logout` - 用户登出
- `GET /api/v1/user/info` - 获取用户信息
- `PUT /api/v1/user/profile` - 更新用户资料
- `POST /api/v1/user/deactivate` - 用户注销

#### 3.1.2 支付模块API
- `POST /api/v1/payment/create-order` - 创建支付订单
- `GET /api/v1/payment/order/{orderNo}` - 查询支付订单
- `POST /api/v1/payment/cancel/{orderNo}` - 取消支付订单
- `POST /api/v1/payment/refund` - 申请退款
- `GET /api/v1/payment/channels` - 获取支付渠道
- `POST /api/v1/payment/callback/{channelCode}` - 支付回调
- `POST /api/v1/payment/sync-status/{orderNo}` - 同步支付状态

### 3.2 响应格式规范

#### 3.2.1 统一响应格式
```java
@Data
@ApiModel("统一响应格式")
public class R<T> {
    @ApiModelProperty("响应码")
    private Integer code;
    
    @ApiModelProperty("响应消息")
    private String message;
    
    @ApiModelProperty("响应数据")
    private T data;
    
    @ApiModelProperty("时间戳")
    private Long timestamp;
    
    @ApiModelProperty("请求ID")
    private String requestId;
    
    public static <T> R<T> success(T data) {
        return new R<>(200, "success", data);
    }
    
    public static <T> R<T> fail(String message) {
        return new R<>(500, message, null);
    }
}
```

### 3.3 缓存管理接口设计

#### 3.3.1 缓存管理控制器 (CacheManagerController.java)
```java
@RestController
@RequestMapping("/api/v1/cache")
@Api(tags = "缓存管理接口")
@SaCheckRole("admin")
public class CacheManagerController {
    
    @DeleteMapping("/clear/{cacheName}")
    @ApiOperation("清空指定缓存")
    public R<Boolean> clearCache(@PathVariable String cacheName);
    
    @PostMapping("/refresh")
    @ApiOperation("刷新指定缓存")
    public R<Boolean> refreshCache(@RequestParam String cacheName, 
                                  @RequestParam String key);
    
    @DeleteMapping("/clear-batch")
    @ApiOperation("批量清空缓存")
    public R<Boolean> clearCaches(@RequestBody List<String> cacheNames);
    
    @GetMapping("/stats/{cacheName}")
    @ApiOperation("获取缓存统计")
    public R<CacheStatsVO> getCacheStats(@PathVariable String cacheName);
    
    @GetMapping("/list")
    @ApiOperation("获取所有缓存列表")
    public R<List<CacheInfoVO>> getCacheList();
}
```
