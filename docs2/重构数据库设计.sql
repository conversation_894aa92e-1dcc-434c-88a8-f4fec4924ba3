-- 超级智能社(SuperAI)重构版数据库设计
-- 基于现有61张表结构，保持核心表不变，新增必要的重构表

-- ================================
-- 重构说明
-- ================================
-- 1. 保持现有核心业务表结构不变
-- 2. 新增用户联合登录相关表
-- 3. 新增统一支付配置表
-- 4. 优化现有表的索引和字段
-- 5. 添加国际化和多币种支持字段

-- ================================
-- 新增表 - 用户联合登录
-- ================================

-- 用户联合登录表 (新增) - 统一管理所有登录方式，替代users和wx_user_info表
CREATE TABLE `user_joint_login` (
  `id` bigint AUTO_INCREMENT COMMENT '主键' PRIMARY KEY,
  `user_id` int NOT NULL COMMENT '关联user_base_info.id',
  `login_type` varchar(20) NOT NULL COMMENT '登录类型(WECHAT/GOOGLE/FACEBOOK/PHONE/EMAIL/PASSWORD/FINGERPRINT)',
  `third_party_id` varchar(100) NOT NULL COMMENT '第三方唯一标识',
  `third_party_username` varchar(100) COMMENT '第三方用户名',
  `third_party_email` varchar(100) COMMENT '第三方邮箱',
  `third_party_avatar` varchar(500) COMMENT '第三方头像',

  -- 微信相关字段 (从wx_user_info表迁移)
  `wechat_open_id` varchar(64) COMMENT '微信OpenID',
  `wechat_union_id` varchar(64) COMMENT '微信UnionID',
  `wechat_session_key` varchar(100) COMMENT '微信会话密钥',
  `gender` int(1) COMMENT '性别(0:未知 1:男 2:女)',
  `country` varchar(50) COMMENT '国家',
  `province` varchar(50) COMMENT '省份',
  `city` varchar(50) COMMENT '城市',
  `language` varchar(20) COMMENT '语言',

  -- 第三方登录字段 (从users表迁移)
  `fb_id` varchar(100) COMMENT 'Facebook ID',
  `google_id` varchar(100) COMMENT 'Google ID',
  `finb_id` varchar(100) COMMENT '浏览器指纹值',
  `referrer_id` bigint COMMENT '推荐人ID',
  `lucky_coins` bigint DEFAULT 0 COMMENT '幸运币',

  -- 通用字段
  `access_token` varchar(500) COMMENT '访问令牌',
  `refresh_token` varchar(500) COMMENT '刷新令牌',
  `token_expire_time` datetime COMMENT '令牌过期时间',
  `extra_info` text COMMENT '扩展信息(JSON格式)',
  `is_primary` tinyint DEFAULT 0 COMMENT '是否主要登录方式(0:否 1:是)',
  `status` tinyint DEFAULT 1 COMMENT '状态(0:禁用 1:启用)',
  `bind_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '绑定时间',
  `last_use_time` datetime COMMENT '最后使用时间',
  `deleted` tinyint DEFAULT 0 COMMENT '是否删除(0:否 1:是)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

  UNIQUE KEY `uk_type_third_id` (`login_type`, `third_party_id`),
  UNIQUE KEY `uk_wechat_open_id` (`wechat_open_id`),
  UNIQUE KEY `uk_fb_id` (`fb_id`),
  UNIQUE KEY `uk_google_id` (`google_id`),
  UNIQUE KEY `uk_finb_id` (`finb_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_login_type` (`login_type`),
  KEY `idx_status` (`status`),
  KEY `idx_deleted` (`deleted`)
) COMMENT '用户联合登录表-统一管理所有登录方式' CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- 用户联合登录配置表 (新增)
CREATE TABLE `user_joint_config` (
  `id` int AUTO_INCREMENT COMMENT '主键' PRIMARY KEY,
  `login_type` varchar(20) NOT NULL COMMENT '登录类型',
  `config_name` varchar(100) NOT NULL COMMENT '配置名称',
  `client_id` varchar(255) NOT NULL COMMENT '客户端ID',
  `client_secret` varchar(255) NOT NULL COMMENT '客户端密钥',
  `redirect_uri` varchar(255) COMMENT '回调地址',
  `scope` varchar(255) COMMENT '授权范围',
  `config_params` text COMMENT '配置参数(JSON格式)',
  `enabled` tinyint DEFAULT 1 COMMENT '是否启用(0:否 1:是)',
  `sort_order` int DEFAULT 0 COMMENT '排序权重',
  `environment` varchar(20) DEFAULT 'prod' COMMENT '适用环境(dev/test/prod)',
  `remark` varchar(500) COMMENT '备注',
  `deleted` tinyint DEFAULT 0 COMMENT '是否删除(0:否 1:是)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  UNIQUE KEY `uk_type_env` (`login_type`, `environment`),
  KEY `idx_enabled` (`enabled`),
  KEY `idx_sort_order` (`sort_order`)
) COMMENT '用户联合登录配置表' CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- ================================
-- 新增表 - 统一支付配置
-- ================================

-- 统一支付订单表 (新增，整合现有多个支付订单表)
CREATE TABLE `payment_order` (
  `id` bigint AUTO_INCREMENT COMMENT '主键' PRIMARY KEY,
  `order_no` varchar(64) NOT NULL COMMENT '订单号',
  `user_id` int NOT NULL COMMENT '用户ID',
  `product_id` bigint NOT NULL COMMENT '产品ID',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `product_type` varchar(20) COMMENT '产品类型',
  `payment_channel` varchar(20) NOT NULL COMMENT '支付渠道(ALIPAY/WECHAT/SEPAY/MOMO)',
  `payment_method` varchar(20) COMMENT '支付方式',
  `amount` decimal(15,2) NOT NULL COMMENT '订单金额',
  `paid_amount` decimal(15,2) COMMENT '实付金额',
  `currency` varchar(10) DEFAULT 'CNY' COMMENT '币种',
  `exchange_rate` decimal(10,6) DEFAULT 1 COMMENT '汇率',
  `status` tinyint DEFAULT 0 COMMENT '订单状态(0:待支付 1:已支付 2:已取消 3:已退款)',
  `third_party_order_no` varchar(100) COMMENT '第三方订单号',
  `third_party_transaction_id` varchar(100) COMMENT '第三方交易号',
  `pay_time` datetime COMMENT '支付时间',
  `expire_time` datetime COMMENT '过期时间',
  `callback_time` datetime COMMENT '回调时间',
  `payment_ip` varchar(50) COMMENT '支付IP',
  `payment_params` text COMMENT '支付参数(JSON格式)',
  `callback_params` text COMMENT '回调参数(JSON格式)',
  `failure_reason` varchar(500) COMMENT '失败原因',
  `remark` varchar(500) COMMENT '备注',
  `deleted` tinyint DEFAULT 0 COMMENT '是否删除(0:否 1:是)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  UNIQUE KEY `uk_order_no` (`order_no`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_payment_channel` (`payment_channel`),
  KEY `idx_create_time` (`create_time`)
) COMMENT '统一支付订单表' CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- 支付渠道配置表 (新增) - 支持多商户按业务场景区分
CREATE TABLE `payment_channel_config` (
  `id` bigint AUTO_INCREMENT COMMENT '主键' PRIMARY KEY,
  `channel_code` varchar(20) NOT NULL COMMENT '渠道代码(ALIPAY/WECHAT/SEPAY/MOMO)',
  `channel_name` varchar(100) NOT NULL COMMENT '渠道名称',
  `payment_method` varchar(20) NOT NULL COMMENT '支付方式(SCAN/H5/APP)',

  -- 业务场景配置 (按产品类型区分商户)
  `business_scene` varchar(20) NOT NULL COMMENT '业务场景(tarot/zns/chatoi)',
  `scene_name` varchar(100) COMMENT '场景名称',

  -- pay-java-parent官方配置字段
  `app_id` varchar(100) COMMENT '应用ID(支付宝AppId/微信AppId)',
  `merchant_id` varchar(100) COMMENT '商户号(支付宝PID/微信MchId)',
  `app_secret` varchar(255) COMMENT '应用密钥(微信MchKey)',
  `private_key` text COMMENT '应用私钥(支付宝/微信)',
  `public_key` text COMMENT '平台公钥(支付宝)',
  `cert_path` varchar(255) COMMENT '证书路径(微信)',

  -- 通用配置
  `api_url` varchar(255) COMMENT 'API地址(支付宝网关)',
  `notify_url` varchar(255) COMMENT '异步通知地址',
  `return_url` varchar(255) COMMENT '同步返回地址',
  `sign_type` varchar(10) DEFAULT 'RSA2' COMMENT '签名类型',

  -- 业务配置
  `supported_currencies` varchar(255) COMMENT '支持币种["CNY","USD","VND"]',
  `enabled` tinyint DEFAULT 1 COMMENT '是否启用(0:否 1:是)',
  `sort_order` int DEFAULT 0 COMMENT '排序权重',
  `environment` varchar(20) DEFAULT 'prod' COMMENT '环境(dev/test/prod)',
  `fee_rate` decimal(5,4) DEFAULT 0 COMMENT '费率',
  `min_amount` decimal(12,2) DEFAULT 0.01 COMMENT '最小金额',
  `max_amount` decimal(12,2) DEFAULT 50000 COMMENT '最大金额',

  -- 扩展配置 (JSON格式存储pay-java-parent特殊配置)
  `extra_config` text COMMENT '扩展配置(JSON格式)',

  `remark` varchar(500) COMMENT '备注',
  `deleted` tinyint DEFAULT 0 COMMENT '是否删除(0:否 1:是)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

  UNIQUE KEY `uk_channel_scene_env` (`channel_code`, `business_scene`, `environment`),
  KEY `idx_business_scene` (`business_scene`),
  KEY `idx_enabled` (`enabled`),
  KEY `idx_sort_order` (`sort_order`)
) COMMENT '支付渠道配置表-支持多商户多场景' CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- ================================
-- 新增表 - 缓存管理
-- ================================

-- 缓存配置表 (新增)
CREATE TABLE `cache_config` (
  `id` int AUTO_INCREMENT COMMENT '主键' PRIMARY KEY,
  `cache_name` varchar(100) NOT NULL COMMENT '缓存名称',
  `cache_type` varchar(20) NOT NULL COMMENT '缓存类型(LOCAL/REDIS/BOTH)',
  `ttl_seconds` int DEFAULT 3600 COMMENT '过期时间(秒)',
  `max_size` int DEFAULT 1000 COMMENT '最大大小',
  `description` varchar(500) COMMENT '描述',
  `enabled` tinyint DEFAULT 1 COMMENT '是否启用',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  UNIQUE KEY `uk_cache_name` (`cache_name`)
) COMMENT '缓存配置表' CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- ================================
-- 新增表 - 国际化支持
-- ================================

-- 国际化消息表 (新增)
CREATE TABLE `i18n_message` (
  `id` bigint AUTO_INCREMENT COMMENT '主键' PRIMARY KEY,
  `message_key` varchar(200) NOT NULL COMMENT '消息键',
  `locale` varchar(10) NOT NULL COMMENT '语言区域',
  `message_value` text NOT NULL COMMENT '消息值',
  `module` varchar(50) COMMENT '所属模块',
  `description` varchar(500) COMMENT '描述',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  UNIQUE KEY `uk_key_locale` (`message_key`, `locale`),
  KEY `idx_module` (`module`)
) COMMENT '国际化消息表' CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- 汇率配置表 (新增)
CREATE TABLE `exchange_rate` (
  `id` int AUTO_INCREMENT COMMENT '主键' PRIMARY KEY,
  `from_currency` varchar(10) NOT NULL COMMENT '源币种',
  `to_currency` varchar(10) NOT NULL COMMENT '目标币种',
  `rate` decimal(12,6) NOT NULL COMMENT '汇率',
  `source` varchar(50) COMMENT '汇率来源',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  UNIQUE KEY `uk_currency_pair` (`from_currency`, `to_currency`)
) COMMENT '汇率配置表' CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- ================================
-- 现有表结构优化 (保持数据不变)
-- ================================

-- 优化user_base_info表 - 添加国际化字段
ALTER TABLE `user_base_info` 
ADD COLUMN `language` varchar(10) DEFAULT 'zh_CN' COMMENT '语言偏好' AFTER `email`,
ADD COLUMN `timezone` varchar(50) DEFAULT 'Asia/Shanghai' COMMENT '时区' AFTER `language`,
ADD COLUMN `currency` varchar(10) DEFAULT 'CNY' COMMENT '币种偏好' AFTER `timezone`,
ADD COLUMN `country_code` varchar(10) COMMENT '国家代码' AFTER `currency`;

-- 优化product表 - 添加多币种支持
ALTER TABLE `product`
ADD COLUMN `original_currency` varchar(10) DEFAULT 'CNY' COMMENT '原始币种' AFTER `currency`,
ADD COLUMN `price_config` text COMMENT '多币种价格配置(JSON)' AFTER `original_currency`;

-- 优化site_info表 - 添加更多监控字段
ALTER TABLE `site_info`
ADD COLUMN `health_check_interval` int DEFAULT 60 COMMENT '健康检查间隔(秒)' AFTER `status`,
ADD COLUMN `last_error_time` datetime COMMENT '最后错误时间' AFTER `health_check_interval`,
ADD COLUMN `consecutive_errors` int DEFAULT 0 COMMENT '连续错误次数' AFTER `last_error_time`;

-- 优化channel_config表 - 添加更多配置字段
ALTER TABLE `channel_config`
ADD COLUMN `circuit_breaker_enabled` tinyint DEFAULT 1 COMMENT '是否启用熔断器' AFTER `status`,
ADD COLUMN `circuit_breaker_threshold` int DEFAULT 5 COMMENT '熔断器阈值' AFTER `circuit_breaker_enabled`,
ADD COLUMN `backup_channel_id` int COMMENT '备用通道ID' AFTER `circuit_breaker_threshold`;

-- ================================
-- 初始化数据
-- ================================

-- 初始化联合登录配置
INSERT INTO `user_joint_config` (`login_type`, `config_name`, `client_id`, `client_secret`, `enabled`, `sort_order`) VALUES
('WECHAT', '微信开放平台', 'your_wechat_app_id', 'your_wechat_app_secret', 1, 1),
('GOOGLE', 'Google OAuth2', 'your_google_client_id', 'your_google_client_secret', 1, 2),
('FACEBOOK', 'Facebook Login', 'your_facebook_app_id', 'your_facebook_app_secret', 1, 3),
('PHONE', '手机号登录', '', '', 1, 4),
('EMAIL', '邮箱登录', '', '', 1, 5),
('PASSWORD', '密码登录', '', '', 1, 6),
('FINGERPRINT', '浏览器指纹', '', '', 1, 7);

-- 初始化支付渠道配置 - pay-java-parent官方配置示例
INSERT INTO `payment_channel_config` VALUES
-- 支付宝配置 (pay-java-parent官方参数)
(1, 'ALIPAY', '支付宝', 'SCAN',
 'your_alipay_app_id',           -- app_id
 'your_alipay_pid',              -- merchant_id
 NULL,                           -- app_secret (支付宝不需要)
 'your_alipay_private_key',      -- private_key
 'alipay_public_key',            -- public_key
 NULL,                           -- cert_path (支付宝不需要)
 'https://openapi.alipay.com/gateway.do', -- api_url
 'https://yourdomain.com/api/payment/callback/alipay',  -- notify_url
 'https://yourdomain.com/payment/return/alipay',        -- return_url
 'RSA2',                         -- sign_type
 '["CNY","USD"]',                -- supported_currencies
 1, 1, 'prod', 0.006, 0.01, 50000,
 '{"timeout":"15m","product_code":"FAST_INSTANT_TRADE_PAY"}', -- extra_config
 '支付宝官方配置', 0, NOW(), NOW()),

-- 微信支付配置 (pay-java-parent官方参数)
(2, 'WECHAT', '微信支付', 'SCAN',
 'your_wechat_app_id',           -- app_id
 'your_wechat_mch_id',           -- merchant_id
 'your_wechat_mch_key',          -- app_secret (微信叫mch_key)
 'your_wechat_private_key',      -- private_key
 NULL,                           -- public_key (微信不需要)
 '/path/to/wechat/cert.p12',     -- cert_path
 'https://api.mch.weixin.qq.com', -- api_url
 'https://yourdomain.com/api/payment/callback/wechat', -- notify_url
 'https://yourdomain.com/payment/return/wechat',       -- return_url
 'MD5',                          -- sign_type
 '["CNY"]',                      -- supported_currencies
 1, 2, 'prod', 0.006, 0.01, 50000,
 '{"trade_type":"NATIVE","timeout":"15m"}', -- extra_config
 '微信支付官方配置', 0, NOW(), NOW()),

-- SE支付配置 (保持现有实现)
(3, 'SEPAY', 'SE支付', 'BANK_TRANSFER',
 NULL, NULL, NULL, NULL, NULL, NULL,
 'https://sepay.vn/api',
 'https://yourdomain.com/api/payment/callback/sepay',
 'https://yourdomain.com/payment/return/sepay',
 NULL, '["VND"]', 1, 3, 'prod', 0.01, 10000, ********,
 '{"bank_code":"VCB","timeout":"30m"}',
 'SE支付越南本地', 0, NOW(), NOW()),

-- Momo支付配置 (保持现有实现)
(4, 'MOMO', 'Momo支付', 'WALLET',
 'your_momo_app_id', 'your_momo_partner_code', 'your_momo_secret_key',
 NULL, NULL, NULL,
 'https://payment.momo.vn/v2/gateway/api',
 'https://yourdomain.com/api/payment/callback/momo',
 'https://yourdomain.com/payment/return/momo',
 'HMAC_SHA256', '["VND"]', 1, 4, 'prod', 0.015, 10000, ********,
 '{"version":"2.0","timeout":"15m"}',
 'Momo支付越南', 0, NOW(), NOW());

-- 初始化缓存配置
INSERT INTO `cache_config` (`cache_name`, `cache_type`, `ttl_seconds`, `max_size`, `description`) VALUES
('user_info', 'BOTH', 1800, 10000, '用户信息缓存'),
('model_channels', 'REDIS', 300, 1000, '模型通道缓存'),
('sys_config', 'BOTH', 3600, 500, '系统配置缓存'),
('payment_channels', 'REDIS', 600, 100, '支付渠道缓存'),
('i18n_messages', 'LOCAL', 7200, 5000, '国际化消息缓存');

-- 初始化国际化消息 (示例)
INSERT INTO `i18n_message` (`message_key`, `locale`, `message_value`, `module`) VALUES
('user.login.success', 'zh_CN', '登录成功', 'user'),
('user.login.success', 'en_US', 'Login successful', 'user'),
('user.login.success', 'vi_VN', 'Đăng nhập thành công', 'user'),
('payment.order.created', 'zh_CN', '订单创建成功', 'payment'),
('payment.order.created', 'en_US', 'Order created successfully', 'payment'),
('payment.order.created', 'vi_VN', 'Đơn hàng được tạo thành công', 'payment');

-- 初始化汇率配置 (汇率设为1，暂不支持转换)
INSERT INTO `exchange_rate` (`from_currency`, `to_currency`, `rate`) VALUES
('CNY', 'USD', 1.0),
('CNY', 'VND', 1.0),
('USD', 'CNY', 1.0),
('USD', 'VND', 1.0),
('VND', 'CNY', 1.0),
('VND', 'USD', 1.0);
