create table al_orders
(
    orders_id      varchar(100)                         not null comment '订单ID'
        primary key,
    user_id        varchar(64)                          not null comment '用户ID',
    product_id     bigint                               not null comment '产品ID',
    product_type   varchar(10)                          null,
    product_name   varchar(50)                          not null comment '产品名称',
    product_price  double                               not null comment '价格',
    num            bigint                               not null comment '数量',
    unit           varchar(10)                          null comment '单位(DAY-天;MONTH-月;YEAR-年;TIMES-次数)',
    package_info   varchar(500)                         null comment '组合套餐信息',
    state          tinyint                              not null comment '状态：1-成功，0-未支付',
    pay_time       datetime                             null comment '支付时间',
    reason_failure varchar(50)                          null comment '失败原因',
    expires_time   datetime                             null comment '过期时间',
    created_time   datetime default current_timestamp() null comment '创建时间',
    update_time    datetime default current_timestamp() null comment '更新时间'
)
    comment '支付宝订单' charset = utf8mb4
                         row_format = DYNAMIC;

create index idx_user
    on al_orders (user_id);

create table app_sign
(
    id               int unsigned auto_increment
        primary key,
    user_id          varchar(48) charset utf8mb3  null comment '用户ID',
    interrupt_status varchar(50) charset utf8mb3  null comment '是否中断',
    check_date       date                         null comment '签到日期',
    repair_check     varchar(2) charset utf8mb3   null comment '补签',
    remark           varchar(255) charset utf8mb3 null comment '备注'
)
    comment 'APP用户签到' charset = utf8mb4
                          row_format = DYNAMIC;

create index inx_user
    on app_sign (user_id);

create table category_info
(
    id     int(11) unsigned auto_increment
        primary key,
    name   varchar(64)      null comment '类别',
    code   varchar(64)      null comment '代码',
    sort   int(5)           null comment '排序',
    status int(5)           null comment '状态',
    remark varchar(255)     null comment '备注',
    type   int(2) default 1 null comment '类型',
    constraint category_info_name_IDX
        unique (name, code)
)
    comment '分类信息' charset = utf8mb4
                       row_format = DYNAMIC;

create table channel_config
(
    id           int auto_increment comment 'ID'
        primary key,
    model_gid    varchar(64)                          not null comment '模型GID',
    status       int(3)   default 0                   not null comment '状态0启用1禁用',
    priority     int                                  not null comment '优先级',
    failed_times int      default 0                   not null comment '失败次数',
    site_id      int                                  null comment '站点ID',
    api_key_name varchar(255)                         null comment '密钥名称',
    site_name    varchar(64)                          null comment '站点名称',
    remark       varchar(255)                         null comment '备注',
    create_time  datetime default current_timestamp() not null comment '创建时间',
    update_time  datetime default current_timestamp() not null on update current_timestamp() comment '更新时间',
    constraint model_site_id
        unique (model_gid, site_id)
)
    comment '通道配置' charset = utf8mb4
                       row_format = DYNAMIC;

create index idx_model_gid
    on channel_config (model_gid);

create table chat_agent
(
    id            int unsigned auto_increment
        primary key,
    title         varchar(100)                                null comment '标题',
    description   varchar(5000)                               null comment '描述',
    tag           varchar(20)                                 null comment '分类',
    status        int(2) unsigned default 0                   null comment '0-启用：可见可用，1-临时停用：不可见不可用，2: 内置应用：不可见可用，9-下架：不可见不可用',
    gid           varchar(128)    default ''                  null comment '模型ID',
    model_name    varchar(64)                                 null comment '模型名称',
    use_cnt       int                                         null comment '使用次数',
    sys_content   text                                        null comment '系统回答',
    input_example varchar(5000)                               null comment '输入提示',
    charge        int(3)          default 0                   null comment '是否收费',
    img_url       varchar(1000)                               null comment '图标',
    hot           int(3)                                      null comment '是否热门',
    feat_recs     int(3)                                      null comment '是否精选推荐(0-否，1-是)',
    max_token     int(255)                                    null comment '回复数',
    temperature   double(11, 2)                               null comment '随机数',
    num_contexts  int                                         null comment '上下文数量',
    create_by     varchar(50)     default ''                  null comment '创建者',
    create_time   datetime        default current_timestamp() null comment '创建时间',
    update_by     varchar(50)     default ''                  null comment '更新者',
    update_time   timestamp       default current_timestamp() null on update current_timestamp() comment '更新时间',
    remark        varchar(50)     default ''                  null comment '备注',
    start_time    datetime                                    null comment '开始时间',
    end_time      datetime                                    null comment '结束时间'
)
    comment '智能体信息' charset = utf8mb4
                         row_format = DYNAMIC;

create index idx_tag
    on chat_agent (tag);

create table chat_message
(
    id              bigint unsigned auto_increment comment '主键'
        primary key,
    parent_msg_id   bigint                               null comment '父消息ID',
    user_id         int                                  null comment '用户ID',
    message_type    int                                  not null comment '消息类型枚举，1-请求，2-回复',
    chat_room_id    bigint                               null comment '对话 id',
    content         text                                 not null comment '消息内容',
    model_gid       varchar(128)                         null comment '模型',
    agent_name      varchar(64)                          null comment '智能体名称',
    agent_id        int                                  null comment '智能体id',
    site_id         int                                  null comment '站点ID',
    site_name       varchar(64)                          null comment '站点名称',
    site_url        varchar(100)                         null comment '站点URL',
    total_tokens    bigint                               null comment '累计 Tokens',
    status          int(5)   default 0                   null comment '状态0初始化1完成',
    ip              varchar(255)                         null comment 'ip',
    open_id         varchar(64)                          null comment '用户ID',
    remark          longtext                             null comment '备注',
    first_char_time datetime                             null comment '第一个字符出现时间',
    create_time     datetime default current_timestamp() not null comment '创建时间',
    update_time     datetime default current_timestamp() not null on update current_timestamp() comment '更新时间'
)
    comment '聊天消息表' charset = utf8mb4
                         row_format = DYNAMIC;

create index idx_openid
    on chat_message (open_id, chat_room_id);

create table chat_room
(
    id              int(11) unsigned auto_increment comment '主键'
        primary key,
    title           varchar(255)                            not null comment '房间名称',
    description     varchar(2550)                           null comment '房间简介',
    sys_content     longtext                                null comment '系统回答',
    ip              varchar(64)                             null comment 'IP',
    open            varchar(3)                              null comment '是否公开',
    role_id         int                                     null comment '角色ID',
    image_url       varchar(500)                            null comment '房间图片',
    open_id         varchar(64)                             not null comment '微信用户ID',
    conversation_id varchar(64)                             null comment '会话 ID',
    type            varchar(20) default 'CHAT'              not null comment '类型 CHAT-聊天, MUSIC-音乐',
    create_time     datetime    default current_timestamp() not null comment '创建时间',
    update_time     datetime    default current_timestamp() not null on update current_timestamp() comment '更新时间',
    user_id         int                                     null comment '用户ID'
)
    comment '聊天室表' charset = utf8mb4
                       row_format = DYNAMIC;

create index open_id
    on chat_room (open_id);

create table commission_identity
(
    id           int auto_increment comment '主键'
        primary key,
    type         varchar(20)                          null comment '类型',
    code         varchar(64)                          null comment '编号',
    status       varchar(10)                          null comment '状态',
    start_time   datetime default current_timestamp() not null comment '生效时间',
    end_time     datetime                             not null comment '过期时间',
    percentage   int(5)                               null comment '分佣比例',
    name         varchar(100)                         null comment '姓名',
    phone        varchar(20)                          null comment '手机号',
    wx_mp_url    varchar(255)                         null comment '微信公众号链接',
    user_info_id int                                  not null comment '用户信息ID',
    open_id      varchar(64)                          not null comment 'openId',
    invite_code  varchar(50)                          not null comment '邀请码',
    create_time  datetime default current_timestamp() not null comment '创建时间',
    update_time  datetime default current_timestamp() not null on update current_timestamp() comment '更新时间'
)
    comment '参与身份' charset = utf8mb4
                       row_format = DYNAMIC;

create table draw_message
(
    id           int(11) unsigned auto_increment comment 'id'
        primary key,
    draw_room_id varchar(64)                                not null comment '绘图板id',
    task_id      varchar(64)                                null comment '任务id',
    user_id      int                                        not null comment '用户id',
    open_id      varchar(64)                                null comment 'open_id',
    action       varchar(20)                                null comment '事件',
    prompt       varchar(2000)                              null comment '提示词',
    prompt_en    varchar(5000)                              null comment '提示词-英文',
    submit_time  datetime                                   null comment '提交时间',
    start_time   datetime                                   null comment '开始时间',
    finish_time  datetime                                   null comment '结束时间',
    image_url    varchar(500)                               null comment '图片地址',
    bot_type     varchar(255)                               null comment '请求地址',
    custom_id    varchar(255)                               null comment '定制请求',
    description  varchar(1000)                              null comment '任务描述',
    state        varchar(255)                               null comment '自定义参数',
    buttons      varchar(4096)                              null comment '按钮',
    status       varchar(255)                               null comment '任务状态',
    hot          varchar(5)                                 null comment '是否热门',
    progress     varchar(255)                               null comment '任务进度',
    fail_reason  varchar(255)                               null comment '失败原因',
    create_by    varchar(500) default ''                    null comment '创建者',
    create_time  datetime     default '1000-01-01 00:00:00' null comment '创建时间',
    update_by    varchar(50)  default ''                    null comment '更新者',
    update_time  timestamp    default current_timestamp()   null on update current_timestamp() comment '更新时间'
)
    comment '任务记录' charset = utf8mb4
                       row_format = DYNAMIC;

create index idx_draw_id
    on draw_message (draw_room_id);

create index idx_openid
    on draw_message (open_id);

create index idx_task_id
    on draw_message (task_id);

create table draw_room
(
    id          int(11) unsigned auto_increment comment '主键'
        primary key,
    title       varchar(255)                         not null comment '绘图板名称',
    description varchar(2550)                        null comment '绘图板简介',
    ip          varchar(64)                          null comment 'IP',
    open        varchar(3)                           null comment '是否公开',
    role_id     int                                  null comment '角色ID',
    image_url   varchar(500)                         null comment '绘图板图片',
    open_id     varchar(64)                          not null comment '用户ID',
    create_time datetime default current_timestamp() not null comment '创建时间',
    update_time datetime default current_timestamp() not null on update current_timestamp() comment '更新时间'
)
    comment '绘图板表' charset = utf8mb4
                       row_format = DYNAMIC;

create index draw_board_open_id
    on draw_room (open_id);

create table exception_log
(
    id           bigint unsigned auto_increment comment '主键'
        primary key,
    message_type varchar(10)                          not null comment '消息类型 CHAT-聊天',
    message_id   int                                  null comment '消息ID',
    user_id      int                                  null comment '用户ID',
    account      varchar(64)                          null comment '账号(手机号或其他账号)',
    name         varchar(10)                          null comment '用户名',
    model_gid    varchar(128)                         null comment '模型ID',
    model_name   varchar(64)                          null comment '模型名称',
    site_id      int                                  null comment '站点ID',
    url          varchar(200)                         null comment '站点地址',
    api_key      varchar(255)                         null comment '秘钥',
    request      text                                 null comment '请求信息',
    res_message  text                                 null comment '响应消息',
    response     text                                 null comment '响应信息',
    exception    text                                 null comment '异常信息',
    create_time  datetime default current_timestamp() not null comment '创建时间',
    update_time  datetime default current_timestamp() null on update current_timestamp() comment '更新时间'
)
    comment '异常日志表' charset = utf8mb4
                         row_format = DYNAMIC;

create table home_config
(
    id            int unsigned auto_increment
        primary key,
    title         varchar(100)                            null comment '标题',
    description   varchar(5000)                           null comment '描述',
    tag           varchar(20)                             null comment '分类',
    tag_name      varchar(50)                             null comment '分类名称',
    input_example varchar(5000)                           null comment '输入提示',
    img_url       varchar(500)                            null comment '图标',
    hot           int(3)                                  null comment '是否热门',
    status        int(3)      default 0                   null comment '是否内置(0不是1是)',
    num           int         default 0                   null comment '使用次数',
    sys_content   text                                    null comment '系统回答',
    search_value  varchar(50) default ''                  null comment '搜索值',
    create_by     varchar(50) default ''                  null comment '创建者',
    create_time   datetime                                null on update current_timestamp() comment '创建时间',
    update_by     varchar(50) default ''                  null comment '更新者',
    update_time   timestamp   default current_timestamp() null on update current_timestamp() comment '更新时间',
    remark        varchar(50) default ''                  null comment '备注'
)
    comment '类别详细信息' charset = utf8mb4
                           row_format = DYNAMIC;

create table intelligent_fav
(
    id          int unsigned auto_increment
        primary key,
    agent_id    int                    null comment '智能体ID',
    create_by   varchar(50) default '' null comment '创建者',
    create_time datetime               null on update current_timestamp() comment '创建时间'
)
    comment '智能体收藏' charset = utf8mb4
                         row_format = DYNAMIC;

create table model
(
    id          int(11) unsigned auto_increment comment '主键'
        primary key,
    channel     varchar(20)                              not null comment '渠道',
    gid         varchar(128) default ''                  not null comment '模型ID',
    model_name  varchar(64)                              not null comment '模型名称',
    owned_by    varchar(255)                             not null comment '归属',
    remark      varchar(255)                             null comment '备注',
    status      varchar(1)   default '0'                 not null comment '状态 0-启动 1-停用',
    create_by   varchar(50)  default ''                  not null comment '创建者',
    create_time datetime     default current_timestamp() not null comment '创建时间',
    update_by   varchar(50)  default ''                  null comment '更新者',
    update_time datetime     default current_timestamp() null on update current_timestamp() comment '更新时间',
    detection   varchar(2)   default '0'                 null comment '是否需要探查 0:不需要 1:需要'
)
    charset = utf8mb4
    row_format = DYNAMIC;

create table product
(
    product_id         bigint auto_increment comment '产品ID'
        primary key,
    product_name       varchar(100)                             not null comment '产品名称',
    channel            varchar(20)                              null comment '充值渠道',
    type               varchar(10)                              null comment '类型(CHAT-对话;DRAW-绘图;COMMON-通用)',
    num                bigint                                   not null comment '数量',
    unit               varchar(10)                              null comment '单位(DAY-天;MONTH-月;YEAR-年;TIMES-次数)',
    remark             varchar(2000)                            null comment '描述',
    button_name        varchar(10)                              null comment '前端按钮名称',
    product_price      decimal(12, 2)                           not null comment '金额',
    package_info       varchar(500)                             null comment '组合套餐信息',
    sort               int(2)                                   null comment '排序',
    start_time         datetime                                 null comment '开始时间',
    end_time           datetime                                 null comment '结束时间',
    status             int        default 1                     not null comment '状态(0启用1禁用)',
    preferred_recharge varchar(2) default '1'                   null comment '推荐充值 0:首选 1:非首选',
    top_icon           varchar(100)                             null comment '顶部图标',
    create_by          varchar(50)                              null comment '创建人',
    create_time        datetime   default current_timestamp()   not null comment '创建时间',
    update_by          varchar(50)                              null comment '更新人',
    update_time        datetime   default current_timestamp()   not null comment '更新时间',
    user_reg_time_s    datetime   default '0000-00-00 00:00:00' not null on update current_timestamp() comment '用户注册时间开始',
    user_reg_time_e    datetime                                 not null comment '用户注册时间截止',
    description        varchar(255)                             null comment '描述',
    currency           varchar(20)                              null comment '币种(国际代号,全大写)'
)
    comment '产品信息' charset = utf8mb4
                       row_format = DYNAMIC;

create table promotion_info
(
    ID                 int auto_increment comment '主键'
        primary key,
    title              varchar(100)                         not null comment '主题',
    description        varchar(500)                         null comment '描述',
    start_time         datetime                             null comment '开始时间',
    end_time           datetime                             null comment '结束时间',
    logo_url           varchar(200)                         null comment 'LOGO图标链接',
    poster_url         varchar(200)                         null comment '海报图片',
    h5_url             varchar(200)                         null comment 'H5活动图',
    related_product_id int                                  null comment '关联产品ID',
    status             int      default 1                   not null comment '状态(0启用1禁用)',
    remark             varchar(500)                         null comment '备注',
    create_by          varchar(30)                          null comment '创建人',
    create_time        datetime default current_timestamp() not null comment '创建时间',
    update_by          varchar(30)                          null comment '更新人',
    update_time        datetime default current_timestamp() not null comment '更新时间',
    ui_conf            varchar(512)                         null comment 'UI配置'
)
    comment '促销活动信息' charset = utf8mb4
                           row_format = DYNAMIC;

create table prompter_info
(
    id            int auto_increment
        primary key,
    industry      varchar(10)      null comment '行业',
    industry_name varchar(100)     null comment '行业名称',
    key_word      varchar(500)     null comment '提词器',
    status        int(5) default 0 null comment '状态'
)
    comment '提词器信息' charset = utf8mb4
                         row_format = DYNAMIC;

create index idx_key
    on prompter_info (key_word, industry_name);

create table qr_payment
(
    id             bigint auto_increment
        primary key,
    user_id        varchar(255)                            null,
    account_number varchar(255)                            not null comment '银行账号',
    bank_name      varchar(255) collate utf8mb4_unicode_ci not null comment '银行名称',
    amount         decimal(15, 2)                          null comment '转账金额',
    content        text collate utf8mb4_unicode_ci         null comment '传输内容',
    qr_code_url    text collate utf8mb4_unicode_ci         not null comment '生成的QR码URL',
    status         varchar(20)                             not null comment 'PENDING, PAID, FAILED   待定、已支付、失败',
    ip_address     varchar(50)                             null comment 'ip地址',
    created_at     datetime                                not null,
    updated_at     datetime                                null,
    unique_id      varchar(255)                            not null,
    constraint unique_id
        unique (unique_id)
)
    comment '二维码生成记录' charset = latin1
                             row_format = DYNAMIC;

create table recharge_log
(
    id            int auto_increment comment '主键'
        primary key,
    user_id       int                                  null comment '用户id',
    product_id    int                                  null comment '产品id',
    recharge_time datetime                             null comment '充值时间',
    recharge_amt  double                               null comment '充值金额',
    start_time    datetime                             null comment '开始时间',
    end_time      datetime                             null comment '截止时间',
    times         int                                  null comment '数量',
    unit          varchar(20)                          null comment '单位',
    package_info  varchar(500)                         null comment '组合套餐信息',
    channel       varchar(10)                          null comment '通道',
    order_id      varchar(255)                         null comment '订单id',
    created_time  datetime default current_timestamp() null comment '创建时间',
    update_time   datetime default current_timestamp() null comment '更新时间'
)
    comment '充值记录表' charset = utf8mb4
                         row_format = DYNAMIC;

create table record_log
(
    id          int unsigned auto_increment
        primary key,
    content     text        not null comment '内容',
    type        int(2)      null comment '类型',
    open_id     varchar(64) null comment '用户ID',
    record_id   int         null comment '记录ID',
    create_time datetime    null comment '创建时间',
    update_time datetime    null comment '更新时间'
)
    comment '记录日志信息' charset = utf8mb3
                           row_format = DYNAMIC;

create table role_msg_template
(
    id        int unsigned auto_increment
        primary key,
    content   varchar(2000) null comment '内容',
    config_id int           null comment '配置ID',
    type      int(1)        null comment '1问题2回答'
)
    comment '角色消息模版' charset = utf8mb3
                           row_format = DYNAMIC;

create table se_pay_order
(
    id               bigint(11) auto_increment
        primary key,
    product_id       bigint(11)                              null comment '商品ID',
    product_type     varchar(10)                             null comment '商品类型',
    unit             varchar(10)                             null comment '单位(DAY-天;MONTH-月;YEAR-年;TIMES-次数)',
    num              bigint                                  null comment '数量',
    body             varchar(255)                            null comment '商品描述',
    order_no         varchar(64)                             null comment '商户订单号',
    unique_id        varchar(100)                            null comment '唯一ID',
    amount           decimal(15, 2)                          null comment '转账金额',
    user_id          varchar(64)                             null comment '用户id',
    status           int(3)      default 0                   null comment '状态',
    account_number   varchar(255)                            not null comment '银行账号',
    bank_name        varchar(255) collate utf8mb4_unicode_ci not null comment '银行名称',
    qr_code_url      text collate utf8mb4_unicode_ci         not null comment '生成的QR码URL',
    ip_address       varchar(50)                             null comment 'ip地址',
    gateway          varchar(100)                            null comment '银行网关名称',
    transaction_date datetime                                null comment '交易时间',
    code             varchar(100)                            null comment '付款代码',
    content          varchar(255)                            null comment '转账内容',
    transfer_type    varchar(50)                             null comment '交易类型：进或出',
    transfer_amount  decimal(15, 2)                          null comment '交易金额',
    accumulated      decimal(15, 2)                          null comment '累计账户余额',
    sub_account      varchar(100)                            null comment '子账户',
    reference_code   varchar(100)                            null comment '参考代码',
    description      text                                    null comment '内容',
    se_pay_id        varchar(64)                             null comment 'sePayId',
    time_end         datetime                                null comment '支付完成时间',
    expires_time     datetime                                null comment '过期时间',
    remark           varchar(50) default ''                  null comment '备注',
    create_by        varchar(50) default ''                  null comment '创建者',
    create_time      datetime    default current_timestamp() null comment '创建时间',
    update_by        varchar(50) default ''                  null comment '更新者',
    update_time      timestamp   default current_timestamp() null on update current_timestamp() comment '更新时间',
    constraint wx_pay_order_unique
        unique (order_no)
)
    comment '支付订单信息' charset = utf8mb4
                           row_format = DYNAMIC;

create table sensitive_word
(
    id          bigint auto_increment comment '主键'
        primary key,
    word        varchar(255)                         not null comment '敏感词内容',
    status      int      default 1                   not null comment '状态 1 启用 2 停用',
    is_deleted  int      default 0                   null comment '是否删除 0 否 NULL 是',
    create_time datetime default current_timestamp() not null on update current_timestamp() comment '创建时间',
    update_time datetime default current_timestamp() not null on update current_timestamp() comment '更新时间'
)
    comment '敏感词表' charset = utf8mb4
                       row_format = DYNAMIC;

create table share_info
(
    id           int                                       not null
        primary key,
    share_openid varchar(64)                               null comment '分享用户',
    click        varchar(64)                               null comment '点击用户',
    status       int(5)                                    null comment '状态',
    search_value varchar(50) default ''                    null comment '搜索值',
    create_by    varchar(50) default ''                    null comment '创建者',
    create_time  datetime    default '1000-01-01 00:00:00' null comment '创建时间',
    update_by    varchar(50) default ''                    null comment '更新者',
    update_time  timestamp   default current_timestamp()   null on update current_timestamp() comment '更新时间',
    remark       varchar(50) default ''                    null comment '备注'
)
    comment '分享点击信息' charset = utf8mb4
                           row_format = DYNAMIC;

create table site_info
(
    id              int unsigned auto_increment comment 'ID'
        primary key,
    name            varchar(64)                          null comment '站点名称',
    url             varchar(1000)                        null comment '站点地址',
    api_key_name    varchar(255)                         null comment '密钥名称',
    api_key         varchar(255)                         null comment '秘钥',
    status          int(2)   default 0                   null comment '状态0启动1禁用',
    remark          varchar(255)                         null comment '备注',
    http_proxy_host varchar(255)                         null comment '代理地址',
    http_proxy_port int                                  null comment '代理端口',
    create_time     datetime default current_timestamp() not null comment '创建时间',
    update_time     datetime default current_timestamp() not null on update current_timestamp() comment '更新时间'
)
    comment '站点信息' charset = utf8mb4
                       row_format = DYNAMIC;

create table sys_config
(
    config_id    int(5) auto_increment comment '参数主键'
        primary key,
    config_name  varchar(100) default ''  null comment '参数名称',
    config_key   varchar(100) default ''  null comment '参数键名',
    config_value varchar(500) default ''  null comment '参数键值',
    config_type  char         default 'N' null comment '系统内置（Y是 N否）',
    create_by    varchar(64)  default ''  null comment '创建者',
    create_time  datetime                 null comment '创建时间',
    update_by    varchar(64)  default ''  null comment '更新者',
    update_time  datetime                 null comment '更新时间',
    remark       varchar(500)             null comment '备注'
)
    comment '参数配置表' charset = utf8mb4
                         row_format = DYNAMIC;

create table sys_dept
(
    dept_id     bigint auto_increment comment '部门id'
        primary key,
    parent_id   bigint      default 0   null comment '父部门id',
    ancestors   varchar(50) default ''  null comment '祖级列表',
    dept_name   varchar(30) default ''  null comment '部门名称',
    order_num   int(4)      default 0   null comment '显示顺序',
    leader      varchar(20)             null comment '负责人',
    phone       varchar(11)             null comment '联系电话',
    email       varchar(50)             null comment '邮箱',
    status      char        default '0' null comment '部门状态（0正常 1停用）',
    del_flag    char        default '0' null comment '删除标志（0代表存在 2代表删除）',
    create_by   varchar(64) default ''  null comment '创建者',
    create_time datetime                null comment '创建时间',
    update_by   varchar(64) default ''  null comment '更新者',
    update_time datetime                null comment '更新时间'
)
    comment '部门表' charset = utf8mb4
                     row_format = DYNAMIC;

create table sys_dict_data
(
    dict_code   bigint auto_increment comment '字典编码'
        primary key,
    dict_sort   int(4)       default 0   null comment '字典排序',
    dict_label  varchar(100) default ''  null comment '字典标签',
    dict_value  varchar(100) default ''  null comment '字典键值',
    dict_type   varchar(100) default ''  null comment '字典类型',
    css_class   varchar(100)             null comment '样式属性（其他样式扩展）',
    list_class  varchar(100)             null comment '表格回显样式',
    is_default  char         default 'N' null comment '是否默认（Y是 N否）',
    status      char         default '0' null comment '状态（0正常 1停用）',
    create_by   varchar(64)  default ''  null comment '创建者',
    create_time datetime                 null comment '创建时间',
    update_by   varchar(64)  default ''  null comment '更新者',
    update_time datetime                 null comment '更新时间',
    remark      varchar(500)             null comment '备注'
)
    comment '字典数据表' charset = utf8mb4
                         row_format = DYNAMIC;

create table sys_dict_type
(
    dict_id     bigint auto_increment comment '字典主键'
        primary key,
    dict_name   varchar(100) default ''  null comment '字典名称',
    dict_type   varchar(100) default ''  null comment '字典类型',
    status      char         default '0' null comment '状态（0正常 1停用）',
    create_by   varchar(64)  default ''  null comment '创建者',
    create_time datetime                 null comment '创建时间',
    update_by   varchar(64)  default ''  null comment '更新者',
    update_time datetime                 null comment '更新时间',
    remark      varchar(500)             null comment '备注',
    constraint dict_type
        unique (dict_type)
)
    comment '字典类型表' charset = utf8mb4
                         row_format = DYNAMIC;

create table sys_job
(
    job_id          bigint auto_increment comment '任务ID',
    job_name        varchar(64)  default ''        not null comment '任务名称',
    job_group       varchar(64)  default 'DEFAULT' not null comment '任务组名',
    invoke_target   varchar(500)                   not null comment '调用目标字符串',
    cron_expression varchar(255) default ''        null comment 'cron执行表达式',
    misfire_policy  varchar(20)  default '3'       null comment '计划执行错误策略（1立即执行 2执行一次 3放弃执行）',
    concurrent      char         default '1'       null comment '是否并发执行（0允许 1禁止）',
    status          char         default '0'       null comment '状态（0正常 1暂停）',
    create_by       varchar(64)  default ''        null comment '创建者',
    create_time     datetime                       null comment '创建时间',
    update_by       varchar(64)  default ''        null comment '更新者',
    update_time     datetime                       null comment '更新时间',
    remark          varchar(500) default ''        null comment '备注信息',
    primary key (job_id, job_name, job_group)
)
    comment '定时任务调度表' charset = utf8mb4
                             row_format = DYNAMIC;

create table sys_job_log
(
    job_log_id     bigint auto_increment comment '任务日志ID'
        primary key,
    job_name       varchar(64)               not null comment '任务名称',
    job_group      varchar(64)               not null comment '任务组名',
    invoke_target  varchar(500)              not null comment '调用目标字符串',
    job_message    varchar(500)              null comment '日志信息',
    status         char          default '0' null comment '执行状态（0正常 1失败）',
    exception_info varchar(2000) default ''  null comment '异常信息',
    create_time    datetime                  null comment '创建时间'
)
    comment '定时任务调度日志表' charset = utf8mb4
                                 row_format = DYNAMIC;

create table sys_logininfor
(
    info_id        bigint auto_increment comment '访问ID'
        primary key,
    user_name      varchar(50)  default ''  null comment '用户账号',
    ipaddr         varchar(128) default ''  null comment '登录IP地址',
    login_location varchar(255) default ''  null comment '登录地点',
    browser        varchar(50)  default ''  null comment '浏览器类型',
    os             varchar(50)  default ''  null comment '操作系统',
    status         char         default '0' null comment '登录状态（0成功 1失败）',
    msg            varchar(255) default ''  null comment '提示消息',
    login_time     datetime                 null comment '访问时间'
)
    comment '系统访问记录' charset = utf8mb4
                           row_format = DYNAMIC;

create index idx_sys_logininfor_lt
    on sys_logininfor (login_time);

create index idx_sys_logininfor_s
    on sys_logininfor (status);

create table sys_menu
(
    menu_id     bigint auto_increment comment '菜单ID'
        primary key,
    menu_name   varchar(50)              not null comment '菜单名称',
    parent_id   bigint       default 0   null comment '父菜单ID',
    order_num   int(4)       default 0   null comment '显示顺序',
    path        varchar(200) default ''  null comment '路由地址',
    component   varchar(255)             null comment '组件路径',
    query       varchar(255)             null comment '路由参数',
    route_name  varchar(50)  default ''  null comment '路由名称',
    is_frame    int(1)       default 1   null comment '是否为外链（0是 1否）',
    is_cache    int(1)       default 0   null comment '是否缓存（0缓存 1不缓存）',
    menu_type   char         default ''  null comment '菜单类型（M目录 C菜单 F按钮）',
    visible     char         default '0' null comment '菜单状态（0显示 1隐藏）',
    status      char         default '0' null comment '菜单状态（0正常 1停用）',
    perms       varchar(100)             null comment '权限标识',
    icon        varchar(100) default '#' null comment '菜单图标',
    create_by   varchar(64)  default ''  null comment '创建者',
    create_time datetime                 null comment '创建时间',
    update_by   varchar(64)  default ''  null comment '更新者',
    update_time datetime                 null comment '更新时间',
    remark      varchar(500) default ''  null comment '备注'
)
    comment '菜单权限表' charset = utf8mb4
                         row_format = DYNAMIC;

create table sys_notice
(
    notice_id      int(4) auto_increment comment '公告ID'
        primary key,
    notice_title   varchar(50)             not null comment '公告标题',
    notice_type    char                    not null comment '公告类型（1通知 2公告）',
    notice_content longblob                null comment '公告内容',
    status         char        default '0' null comment '公告状态（0正常 1关闭）',
    create_by      varchar(64) default ''  null comment '创建者',
    create_time    datetime                null comment '创建时间',
    update_by      varchar(64) default ''  null comment '更新者',
    update_time    datetime                null comment '更新时间',
    remark         varchar(255)            null comment '备注'
)
    comment '通知公告表' charset = utf8mb4
                         row_format = DYNAMIC;

create table sys_oper_log
(
    oper_id        bigint auto_increment comment '日志主键'
        primary key,
    title          varchar(50)   default '' null comment '模块标题',
    business_type  int(2)        default 0  null comment '业务类型（0其它 1新增 2修改 3删除）',
    method         varchar(200)  default '' null comment '方法名称',
    request_method varchar(10)   default '' null comment '请求方式',
    operator_type  int(1)        default 0  null comment '操作类别（0其它 1后台用户 2手机端用户）',
    oper_name      varchar(50)   default '' null comment '操作人员',
    dept_name      varchar(50)   default '' null comment '部门名称',
    oper_url       varchar(255)  default '' null comment '请求URL',
    oper_ip        varchar(128)  default '' null comment '主机地址',
    oper_location  varchar(255)  default '' null comment '操作地点',
    oper_param     varchar(2000) default '' null comment '请求参数',
    json_result    varchar(2000) default '' null comment '返回参数',
    status         int(1)        default 0  null comment '操作状态（0正常 1异常）',
    error_msg      varchar(2000) default '' null comment '错误消息',
    oper_time      datetime                 null comment '操作时间',
    cost_time      bigint        default 0  null comment '消耗时间'
)
    comment '操作日志记录' charset = utf8mb4
                           row_format = DYNAMIC;

create index idx_sys_oper_log_bt
    on sys_oper_log (business_type);

create index idx_sys_oper_log_ot
    on sys_oper_log (oper_time);

create index idx_sys_oper_log_s
    on sys_oper_log (status);

create table sys_post
(
    post_id     bigint auto_increment comment '岗位ID'
        primary key,
    post_code   varchar(64)            not null comment '岗位编码',
    post_name   varchar(50)            not null comment '岗位名称',
    post_sort   int(4)                 not null comment '显示顺序',
    status      char                   not null comment '状态（0正常 1停用）',
    create_by   varchar(64) default '' null comment '创建者',
    create_time datetime               null comment '创建时间',
    update_by   varchar(64) default '' null comment '更新者',
    update_time datetime               null comment '更新时间',
    remark      varchar(500)           null comment '备注'
)
    comment '岗位信息表' charset = utf8mb4
                         row_format = DYNAMIC;

create table sys_role
(
    role_id             bigint auto_increment comment '角色ID'
        primary key,
    role_name           varchar(30)             not null comment '角色名称',
    role_key            varchar(100)            not null comment '角色权限字符串',
    role_sort           int(4)                  not null comment '显示顺序',
    data_scope          char        default '1' null comment '数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）',
    menu_check_strictly tinyint(1)  default 1   null comment '菜单树选择项是否关联显示',
    dept_check_strictly tinyint(1)  default 1   null comment '部门树选择项是否关联显示',
    status              char                    not null comment '角色状态（0正常 1停用）',
    del_flag            char        default '0' null comment '删除标志（0代表存在 2代表删除）',
    create_by           varchar(64) default ''  null comment '创建者',
    create_time         datetime                null comment '创建时间',
    update_by           varchar(64) default ''  null comment '更新者',
    update_time         datetime                null comment '更新时间',
    remark              varchar(500)            null comment '备注'
)
    comment '角色信息表' charset = utf8mb4
                         row_format = DYNAMIC;

create table sys_role_dept
(
    role_id bigint not null comment '角色ID',
    dept_id bigint not null comment '部门ID',
    primary key (role_id, dept_id)
)
    comment '角色和部门关联表' charset = utf8mb4
                               row_format = DYNAMIC;

create table sys_role_menu
(
    role_id bigint not null comment '角色ID',
    menu_id bigint not null comment '菜单ID',
    primary key (role_id, menu_id)
)
    comment '角色和菜单关联表' charset = utf8mb4
                               row_format = DYNAMIC;

create table sys_user
(
    user_id     bigint auto_increment comment '用户ID'
        primary key,
    dept_id     bigint                    null comment '部门ID',
    user_name   varchar(30)               not null comment '用户账号',
    nick_name   varchar(30)               not null comment '用户昵称',
    user_type   varchar(2)   default '00' null comment '用户类型（00系统用户）',
    email       varchar(50)  default ''   null comment '用户邮箱',
    phonenumber varchar(11)  default ''   null comment '手机号码',
    sex         char         default '0'  null comment '用户性别（0男 1女 2未知）',
    avatar      varchar(100) default ''   null comment '头像地址',
    password    varchar(100) default ''   null comment '密码',
    status      char         default '0'  null comment '帐号状态（0正常 1停用）',
    del_flag    char         default '0'  null comment '删除标志（0代表存在 2代表删除）',
    login_ip    varchar(128) default ''   null comment '最后登录IP',
    login_date  datetime                  null comment '最后登录时间',
    create_by   varchar(64)  default ''   null comment '创建者',
    create_time datetime                  null comment '创建时间',
    update_by   varchar(64)  default ''   null comment '更新者',
    update_time datetime                  null comment '更新时间',
    remark      varchar(500)              null comment '备注'
)
    comment '用户信息表' charset = utf8mb4
                         row_format = DYNAMIC;

create table sys_user_post
(
    user_id bigint not null comment '用户ID',
    post_id bigint not null comment '岗位ID',
    primary key (user_id, post_id)
)
    comment '用户与岗位关联表' charset = utf8mb4
                               row_format = DYNAMIC;

create table sys_user_role
(
    user_id bigint not null comment '用户ID',
    role_id bigint not null comment '角色ID',
    primary key (user_id, role_id)
)
    comment '用户和角色关联表' charset = utf8mb4
                               row_format = DYNAMIC;

create table tarot_card_meaning
(
    id             int auto_increment comment '主键'
        primary key,
    name           varchar(20)                          not null comment '名称',
    meaning        text                                 null comment '含义',
    tag            varchar(10)                          null comment '分类',
    guidance_text  varchar(255)                         null comment '指引语',
    advice         varchar(255)                         null comment '建议',
    discouraged    varchar(255)                         null comment '不建议',
    card_front_url varchar(255)                         null comment '正面图',
    remark         varchar(255)                         null comment '备注',
    create_by      varchar(64)                          null comment '创建人',
    create_time    datetime default current_timestamp() null comment '创建时间',
    update_by      varchar(64)                          null comment '更新人',
    update_time    datetime default current_timestamp() null on update current_timestamp() comment '更新时间',
    sort           int(5)                               null comment '排序'
)
    comment '塔罗牌牌义' charset = utf8mb4
                         row_format = DYNAMIC;

create table tarot_daily_insight
(
    id           int auto_increment comment '主键'
        primary key,
    user_id      int(20)                              not null comment '用户id',
    open_id      varchar(64)                          null comment '微信openid',
    card_id      int(20)                              not null comment '今日牌面',
    position     varchar(10)                          not null comment '正逆位;upright-正位， reversed-逆位',
    lucky_color  varchar(255)                         null comment '幸运色',
    lucky_number varchar(255)                         null comment '幸运数',
    create_by    varchar(64)                          null comment '创建人',
    create_time  datetime default current_timestamp() null comment '创建时间',
    update_by    varchar(64)                          null comment '更新人',
    update_time  datetime default current_timestamp() null on update current_timestamp() comment '更新时间',
    insight_date varchar(20)                          null comment '创建时间',
    constraint tarot_daily_insight_unique
        unique (user_id, insight_date)
)
    comment '塔罗今日指引' charset = utf8mb4
                           row_format = DYNAMIC;

create table tarot_i18n
(
    id          bigint auto_increment
        primary key,
    lang        varchar(20)                          not null comment '语言',
    code        text                                 not null comment '编码，同一段文字的不同语言翻译的code必须相同，code可以为中文',
    value       text                                 not null comment '翻译值',
    create_time datetime default current_timestamp() null comment '录入时间',
    update_time datetime default current_timestamp() null comment '修改时间'
)
    comment '翻译字典表' charset = utf8mb4
                         row_format = DYNAMIC;

create table tarot_reading_record
(
    id                  int auto_increment comment '主键'
        primary key,
    user_id             varchar(20)                            null comment '用户id',
    spread_id           varchar(255)                           not null comment '牌阵id',
    question            varchar(50)                            not null comment '问题',
    answer              varchar(4000)                          null,
    draw_result         varchar(2000)                          not null comment '抽牌结果',
    consume             int                                    not null comment '消耗',
    conversation_id     varchar(64)                            null comment '会话 ID',
    status              varchar(2) default '0'                 null comment '状态值 0:未回答;1:已回答',
    create_by           varchar(64)                            null comment '创建人',
    create_time         datetime   default current_timestamp() null comment '创建时间',
    update_by           varchar(64)                            null comment '更新人',
    update_time         datetime   default current_timestamp() null on update current_timestamp() comment '更新时间',
    interpretation_mode varchar(1) default '0'                 null comment '解读模式 0:抽牌模式1:自选牌模式',
    error_msg           varchar(1024)                          null comment '异常信息',
    deleted             tinyint(1) default 0                   not null comment '0不删、1删除'
)
    comment '塔罗牌占卜记录' charset = utf8mb4
                             row_format = DYNAMIC;

create table tarot_spread
(
    id                 int(20) auto_increment comment '主键'
        primary key,
    name               varchar(20)                            not null comment '名称',
    spread_layout      varchar(32)                            null comment '布局;枚举',
    spread_diagram_url varchar(255)                           null comment '示意图',
    summary            varchar(64)                            null comment '简述',
    description        varchar(900)                           null comment '描述',
    input_example      varchar(255)                           null comment '输入示例',
    consume            int                                    null comment '消耗',
    gid                varchar(128)                           null comment '模型id',
    model_key          varchar(128)                           null comment '模型key值',
    time_set           int                                    null comment '告警阈值设置',
    status             varchar(2)                             null comment '状态',
    create_by          varchar(64)                            null comment '创建人',
    create_time        datetime default current_timestamp()   null comment '创建时间',
    update_by          varchar(64)                            null comment '更新人',
    update_time        datetime default current_timestamp()   null on update current_timestamp() comment '更新时间',
    sort               int(5)                                 null comment '排序',
    is_new             varchar(2)                             null comment '是否上新 0:上新 1:非上新',
    user_reg_time_s    datetime default '0000-00-00 00:00:00' not null on update current_timestamp() comment '用户注册时间开始',
    user_reg_time_e    datetime                               not null comment '用户注册时间截止'
)
    comment '塔罗牌牌阵' charset = utf8mb4
                         row_format = DYNAMIC;

create table task_record
(
    id          int(11) unsigned auto_increment comment 'id'
        primary key,
    open_id     varchar(64)                                not null comment 'open_id',
    task_id     varchar(64)                                not null comment '任务id',
    prompt      varchar(1000)                              null comment '提示词',
    prompt_en   varchar(1000)                              null comment '提示词-英文',
    description varchar(1000)                              null comment '任务描述',
    state       varchar(255)                               null comment '自定义参数',
    submit_time datetime                                   null comment '提交时间',
    image_url   varchar(500)                               null comment '图片地址',
    start_time  datetime                                   null comment '开始时间',
    finish_time datetime                                   null comment '结束时间',
    status      varchar(255)                               null comment '任务状态',
    hot         varchar(5)                                 null comment '是否热门',
    progress    varchar(255)                               null comment '任务进度',
    fail_reason varchar(255)                               null comment '失败原因',
    create_by   varchar(500) default ''                    null comment '创建者',
    create_time datetime     default '1000-01-01 00:00:00' null comment '创建时间',
    update_by   varchar(50)  default ''                    null comment '更新者',
    update_time timestamp    default current_timestamp()   null on update current_timestamp() comment '更新时间'
)
    comment '任务记录' charset = utf8mb4
                       row_format = DYNAMIC;

create index idx_openid
    on task_record (open_id);

create index idx_task_id
    on task_record (task_id);

create table transaction
(
    id               int auto_increment
        primary key,
    gateway          varchar(100)   null comment '银行网关名称',
    transaction_date datetime       null comment '交易时间',
    account_number   varchar(50)    null comment '银行账号',
    code             varchar(100)   null comment '付款代码',
    content          varchar(255)   null comment '转账内容',
    transfer_type    varchar(50)    null comment '交易类型：进或出',
    transfer_amount  decimal(15, 2) null comment '交易金额',
    accumulated      decimal(15, 2) null comment '累计账户余额',
    sub_account      varchar(100)   null comment '子账户',
    reference_code   varchar(100)   null comment '短信参考代码',
    description      text           null comment '短信内容',
    product_id       varchar(50)    null comment '产品ID',
    unique_id        varchar(100)   null comment '唯一ID'
)
    comment '交易表' charset = latin1
                     row_format = DYNAMIC;

create table transfer_info
(
    id                  int auto_increment
        primary key,
    open_id             varchar(50)                             not null comment 'openId',
    out_detail_no       varchar(50)                             not null comment '提现订单号',
    user_id             varchar(50) default ''                  null comment '用户id',
    nick_name           varchar(50) default ''                  null comment '用户昵称',
    transfer_points     varchar(255)                            null comment '提现奖励金',
    transfer_amount     varchar(255)                            null comment '提现金额',
    transfer_remark     varchar(255)                            null comment '提现备注',
    user_name           varchar(50) default ''                  null comment '用户姓名',
    status              varchar(2)  default ''                  null comment '提现状态 0:未提现 1:已提现',
    transfer_start_time datetime    default current_timestamp() null comment '提现申请时间',
    transfer_end_time   datetime                                null comment '提现到账日期',
    out_batch_no        varchar(50)                             null comment '转账发起批次号',
    out_batch_time      datetime                                null comment '转账申请发起时间',
    fail_reason         varchar(255)                            null comment '转账失败原因',
    create_by           varchar(50) default ''                  null comment '创建者',
    create_time         datetime    default current_timestamp() null comment '创建时间',
    update_by           varchar(50) default ''                  null comment '更新者',
    update_time         timestamp   default current_timestamp() null on update current_timestamp() comment '更新时间'
)
    comment '提现申请信息' charset = utf8mb4
                           row_format = DYNAMIC;

create table user_base_info
(
    id              int auto_increment comment '主键'
        primary key,
    commission_id   int                       null comment '本表ID',
    account         varchar(64)               not null comment '账号(手机号或其他账号)',
    user_type       varchar(50) default 'zns' null comment '用户类型  zns:智能社 tarot:塔罗',
    name            varchar(15)               null comment '用户名',
    nick_name       varchar(50)               null comment '昵称',
    parent_id       int                       null comment '上级用户id（邀请人id）',
    password        varchar(128)              null comment '密码',
    vip_end_time    datetime                  null comment 'vip到期时间',
    points          int                       null comment '积分',
    draw_num        int         default 0     null comment '绘画次数',
    music_num       int         default 0     not null comment '音乐次数',
    write_num       int                       null comment '写作次数',
    tarot_coins     int         default 0     null comment '塔罗币',
    login_time      datetime                  null comment '最后登录时间',
    head_sculpture  varchar(255)              null comment '头像',
    first_status    varchar(1)                null comment '是否首次登录',
    ip              varchar(28)               null comment 'IP地址',
    address         varchar(255)              null comment '地址',
    email           varchar(128)              null comment '邮箱',
    status          tinyint(2)  default 0     null comment '状态0正常1禁用',
    use_num         int         default 0     null comment '剩余使用次数(充值)',
    open_id         varchar(64)               null comment '微信openid',
    free_num        int         default 0     null comment '免费使用次数(赠送)',
    daily_free_time int                       null comment '每日免费次数',
    remark          varchar(255)              null comment '备注',
    create_time     datetime                  not null comment '创建时间',
    update_time     datetime                  null comment '更新时间',
    users_id        bigint                    null comment 'users表id',
    deleted         tinyint(1)  default 0     not null comment '是否注销（0否1是）',
    constraint idx_account
        unique (account, user_type)
)
    comment '用户基础信息' charset = utf8mb4
                           row_format = DYNAMIC;

create table user_check_in_record
(
    id            int auto_increment comment '主键'
        primary key,
    user_id       int(20)                                not null comment '用户id',
    check_in_date varchar(8)                             not null comment '签到日期;yyyyMMDD',
    week          varchar(20)                            null comment '签到星期',
    open_id       varchar(64)                            null comment '微信openid',
    type          varchar(32)                            not null comment '类型;TAROT-塔罗牌签到',
    is_make_up    varchar(1) default '0'                 not null comment '是否补签;0-否; 1-是，默认0',
    awarded       int                                    not null comment '奖励',
    create_by     varchar(64)                            null comment '创建人',
    create_time   datetime   default current_timestamp() null comment '创建时间',
    update_by     varchar(64)                            null comment '更新人',
    update_time   datetime   default current_timestamp() null on update current_timestamp() comment '更新时间'
)
    comment '用户签到记录' charset = utf8mb4
                           row_format = DYNAMIC;

create table user_config
(
    id      int auto_increment
        primary key,
    openid  varchar(64) null comment '用户ID',
    content text        null comment '配置信息',
    type    int         null comment '配置类型'
)
    comment '用户配置相关信息' charset = utf8mb4
                               row_format = DYNAMIC;

create table user_merge_info
(
    id            int auto_increment comment '主键'
        primary key,
    merge_id      varchar(20)                          not null comment '合并后用户id',
    was_merged_id varchar(20)                          null comment '被合并id',
    create_time   datetime default current_timestamp() not null comment '创建时间'
)
    comment '用户合并记录' charset = utf8mb4
                           row_format = DYNAMIC;

create table user_points_log
(
    id          int auto_increment comment '主键'
        primary key,
    user_id     int                                       not null comment '用户id',
    rel_order   varchar(255)                              not null comment '关联订单',
    points      int                                       null comment '积分',
    points_type varchar(20)                               null comment '积分类型',
    remark      varchar(255)                              null comment '备注',
    create_by   varchar(50) default ''                    null comment '创建者',
    create_time datetime    default '1000-01-01 00:00:00' null comment '创建时间',
    update_by   varchar(50) default ''                    null comment '更新者',
    update_time datetime    default current_timestamp()   null on update current_timestamp() comment '更新时间'
)
    comment '用户积分日志表' charset = utf8mb4
                             row_format = DYNAMIC;

create table users
(
    id              bigint auto_increment
        primary key,
    fb_id           varchar(100)  null comment 'Facebook ID',
    name            varchar(100)  null comment '用户名',
    email           varchar(100)  null comment '邮箱',
    picture         varchar(255)  null comment '头像URL',
    access_token    varchar(255)  null comment 'Facebook访问令牌',
    last_login_time datetime      null comment '最后登录时间',
    created_at      datetime      null comment '创建时间',
    updated_at      datetime      null comment '更新时间',
    referrer_id     bigint        null comment '
推荐人id',
    lucky_coins     bigint        null comment '幸运币',
    google_id       varchar(100)  null comment '谷歌id',
    finb_id         varchar(100)  null comment '浏览器指纹值',
    extra_data      varchar(2000) null comment '额外值',
    constraint uk_email
        unique (email),
    constraint uk_fb_id
        unique (fb_id),
    constraint uk_finb_id
        unique (finb_id)
)
    comment '用户表' charset = utf8mb4
                     row_format = DYNAMIC;

create table write_agent
(
    id            int unsigned auto_increment comment '主键'
        primary key,
    title         varchar(100)                                 not null comment '标题',
    description   varchar(5000)                                null comment '描述',
    tag           varchar(20)                                  not null comment '分类',
    status        int(2)           default 0                   not null comment '0-启用：可见可用，1-临时停用：不可见不可用，2: 内置应用：不可见可用，9-下架：不可见不可用',
    gid           varchar(128)     default ''                  null comment '模型id',
    agent_name    varchar(128)     default ''                  null comment '模型',
    img_url       varchar(1000)                                null comment '图标',
    use_cnt       int(11) unsigned default 0                   null comment '使用次数',
    input_example varchar(5000)                                null comment '输入提示',
    sys_content   text                                         null comment '系统回答',
    charge        int(3)           default 0                   null comment '是否收费',
    consume       varchar(200)                                 null comment '消耗点数',
    hot           int(3)                                       null comment '是否热门',
    feat_recs     int(3)           default 0                   not null comment '精选推荐',
    max_token     int                                          null comment '最大回复数',
    temperature   double(11, 2)                                null comment '随机数',
    layout_conf   varchar(4000)                                null comment '界面',
    remark        varchar(50)      default ''                  null comment '备注',
    create_by     varchar(50)      default ''                  null comment '创建者',
    create_time   timestamp        default current_timestamp() null comment '创建时间',
    update_by     varchar(50)      default ''                  null comment '更新者',
    update_time   timestamp        default current_timestamp() null on update current_timestamp() comment '更新时间',
    start_time    datetime                                     null comment '开始时间',
    end_time      datetime                                     null comment '结束时间'
)
    comment '写作应用' charset = utf8mb4
                       row_format = DYNAMIC;

create index idx_tag
    on write_agent (tag);

create table write_category
(
    id            int unsigned auto_increment comment '主键'
        primary key,
    category_name varchar(50)   not null comment '分类名称',
    icon_url      varchar(255)  null comment '图标URL',
    sort_order    int unsigned  not null comment '排序顺序',
    start_time    datetime      null comment '开始时间',
    end_time      datetime      null comment '结束时间',
    status        int default 1 not null comment '状态(0启用1禁用)',
    constraint idx_category_name
        unique (category_name)
)
    comment '写作分类表' charset = utf8mb4
                         row_format = DYNAMIC;

create table write_message
(
    id              int unsigned auto_increment comment '主键'
        primary key,
    user_id         int                                         not null comment '用户ID',
    topic           varchar(2000)                               null comment '主题',
    inputs          varchar(5000)                               not null comment '请求(json格式的参数)',
    content         text                                        null comment '文章内容',
    model           varchar(128)                                null comment '模型',
    agent_id        int                                         null comment '智能体id',
    agent_title     varchar(100)                                null comment '应用标题',
    agent_name      varchar(128)                                null comment '应用名称',
    consume         int                                         null comment '消耗点数',
    site_id         int                                         null comment '站点ID',
    site_name       varchar(64)                                 null comment '站点名称',
    site_url        varchar(100)                                null comment '站点URL',
    total_tokens    bigint                                      null comment '累计 Tokens',
    status          int(5)          default 0                   null comment '状态0初始化1完成',
    ip              varchar(50)                                 null comment 'ip',
    remark          longtext                                    null comment '备注',
    is_delete       int(2) unsigned default 0                   null comment '删除状态，0为未删除可被查询，1为被删除不可查询',
    first_char_time datetime                                    null comment '第一个字符出现时间',
    create_by       int                                         null,
    create_time     datetime        default current_timestamp() not null comment '创建时间',
    update_by       int                                         null,
    update_time     datetime        default current_timestamp() not null on update current_timestamp() comment '更新时间'
)
    comment '写作记录表' charset = utf8mb4
                         row_format = DYNAMIC;

create table wx_pay_order
(
    id               int auto_increment
        primary key,
    goods_id         int                                     null comment '商品ID',
    product_type     varchar(10)                             null comment '商品类型',
    unit             varchar(10)                             null comment '单位(DAY-天;MONTH-月;YEAR-年;TIMES-次数)',
    num              bigint                                  null comment '数量',
    status           int(3)      default 0                   null comment '状态',
    body             varchar(255)                            null comment '商品描述',
    out_trade_no     varchar(64)                             null comment '商户订单号',
    total_fee        decimal(11, 2)                          null comment '总金额',
    spbill_create_ip varchar(255)                            null comment '终端IP',
    notify_url       varchar(255)                            null comment '通知地址',
    trade_type       varchar(255)                            null comment '交易类型',
    openid           varchar(64)                             null comment '用户标识',
    user_id          varchar(64)                             null comment '用户id',
    transaction_id   varchar(64)                             null comment '微信支付订单号',
    is_subscribe     varchar(255)                            null comment '是否关注公众账号',
    original_message text                                    null comment '原始报文',
    time_end         datetime                                null comment '支付完成时间',
    expires_time     datetime                                null comment '过期时间',
    remark           varchar(50) default ''                  null comment '备注',
    create_by        varchar(50) default ''                  null comment '创建者',
    create_time      datetime    default current_timestamp() null comment '创建时间',
    update_by        varchar(50) default ''                  null comment '更新者',
    update_time      timestamp   default current_timestamp() null on update current_timestamp() comment '更新时间',
    trade_channel    varchar(20)                             null comment '交易渠道',
    currency         varchar(20)                             null comment '交易币种(国际代号,全大写)',
    constraint wx_pay_order_unique
        unique (out_trade_no)
)
    comment '支付订单信息' charset = utf8mb4
                           row_format = DYNAMIC;

create table wx_user_info
(
    open_id         varchar(50)                               not null comment 'openId'
        primary key,
    app_id          varchar(50)                               not null comment 'appId',
    nick_name       varchar(50)   default ''                  null comment '用户昵称',
    subscribe_scene varchar(255)                              null comment '渠道来源',
    qr_scene        varchar(255)                              null comment '二维码扫码场',
    gender          varchar(50)   default ''                  null comment '用户性别',
    language        varchar(50)   default ''                  null comment '语言',
    city            varchar(50)   default ''                  null comment '用户所在城市',
    union_id        varchar(50)   default ''                  null comment 'unionId',
    province        varchar(50)   default ''                  null comment '用户所在省份',
    country         varchar(50)   default ''                  null comment '用户所在国家',
    avatar_url      varchar(300)  default ''                  null comment '用户头像图片',
    vip_end_time    datetime                                  null comment 'vip到期时间',
    apply_num       int                                       null comment '使用剩余次数',
    parent_id       varchar(50)                               null comment '父ID',
    vip             int           default 0                   null comment '是否VIP',
    search_value    varchar(50)   default ''                  null comment '搜索值',
    create_by       varchar(50)   default ''                  null comment '创建者',
    create_time     datetime      default current_timestamp() null comment '创建时间',
    update_by       varchar(50)   default ''                  null comment '更新者',
    update_time     timestamp     default current_timestamp() null on update current_timestamp() comment '更新时间',
    remark          varchar(1000) default ''                  null comment '备注'
)
    comment '微信用户信息' charset = utf8mb4
                           row_format = DYNAMIC;

create index index_openid
    on wx_user_info (open_id);

create index index_uuid
    on wx_user_info (union_id);

