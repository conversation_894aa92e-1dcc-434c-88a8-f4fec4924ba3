# pay-java-parent官方集成说明

## 1. 集成概述

### 1.1 集成策略
基于pay-java-parent官方提供的成熟支付API，直接使用官方实现，无需重写现有支付代码。这样可以：
- ✅ **降低开发成本**: 无需重新开发支付逻辑
- ✅ **提高稳定性**: 使用经过大量验证的官方代码
- ✅ **享受官方更新**: 自动获得官方bug修复和功能更新
- ✅ **减少安全风险**: 避免自研支付代码的安全漏洞

### 1.2 支持的支付方式
```yaml
官方支持 (直接使用):
  - 支付宝: AliPayServiceImpl
  - 微信支付: WxPayServiceImpl
  
保持现有实现:
  - SE支付: 越南本地银行转账
  - Momo支付: 越南电子钱包
```

## 2. 依赖配置

### 2.1 Maven依赖
```xml
<!-- pay-java-parent 核心依赖 -->
<dependency>
    <groupId>com.egzosn</groupId>
    <artifactId>pay-java-ali</artifactId>
    <version>2.13.4</version>
</dependency>

<dependency>
    <groupId>com.egzosn</groupId>
    <artifactId>pay-java-wx</artifactId>
    <version>2.13.4</version>
</dependency>

<!-- 可选：支付宝SDK (如需要更多功能) -->
<dependency>
    <groupId>com.alipay.sdk</groupId>
    <artifactId>alipay-sdk-java</artifactId>
    <version>4.35.79.ALL</version>
</dependency>

<!-- 可选：微信支付SDK (如需要更多功能) -->
<dependency>
    <groupId>com.github.wechatpay-apiv3</groupId>
    <artifactId>wechatpay-java</artifactId>
    <version>0.2.12</version>
</dependency>
```

### 2.2 Spring Boot配置
```yaml
# application.yml
pay:
  alipay:
    # 支付宝配置将从数据库读取，这里只是示例
    app-id: ${ALIPAY_APP_ID:your_app_id}
    private-key: ${ALIPAY_PRIVATE_KEY:your_private_key}
    public-key: ${ALIPAY_PUBLIC_KEY:alipay_public_key}
    server-url: https://openapi.alipay.com/gateway.do
    sign-type: RSA2
    
  wechat:
    # 微信支付配置将从数据库读取，这里只是示例
    app-id: ${WECHAT_APP_ID:your_app_id}
    mch-id: ${WECHAT_MCH_ID:your_mch_id}
    mch-key: ${WECHAT_MCH_KEY:your_mch_key}
    cert-path: ${WECHAT_CERT_PATH:/path/to/cert.p12}
```

## 3. 核心实现

### 3.1 支付服务工厂
```java
@Component
@Slf4j
public class PayServiceFactory {
    
    @Autowired
    private PaymentChannelConfigService channelConfigService;
    
    /**
     * 获取支付服务 - 基于pay-java-parent官方实现
     */
    public PayService getPayService(String channelCode) {
        PaymentChannelConfig config = channelConfigService.getEnabledConfig(channelCode);
        
        switch (channelCode.toUpperCase()) {
            case "ALIPAY":
                return createAlipayService(config);
            case "WECHAT":
                return createWechatPayService(config);
            default:
                throw new ServiceException("不支持的支付渠道: " + channelCode);
        }
    }
    
    /**
     * 创建支付宝支付服务 - 使用pay-java-parent官方配置
     */
    private PayService createAlipayService(PaymentChannelConfig config) {
        AliPayConfig aliPayConfig = AliPayConfig.builder()
            .appId(config.getAppId())
            .privateKey(config.getPrivateKey())
            .publicKey(config.getPublicKey())
            .serverUrl(config.getApiUrl())
            .signType(config.getSignType())
            .notifyUrl(config.getNotifyUrl())
            .returnUrl(config.getReturnUrl())
            .build();
            
        return new AliPayServiceImpl(aliPayConfig);
    }
    
    /**
     * 创建微信支付服务 - 使用pay-java-parent官方配置
     */
    private PayService createWechatPayService(PaymentChannelConfig config) {
        WxPayConfig wxPayConfig = WxPayConfig.builder()
            .appId(config.getAppId())
            .mchId(config.getMerchantId())
            .mchKey(config.getAppSecret())
            .certPath(config.getCertPath())
            .notifyUrl(config.getNotifyUrl())
            .returnUrl(config.getReturnUrl())
            .build();
            
        return new WxPayServiceImpl(wxPayConfig);
    }
}
```

### 3.2 统一支付服务
```java
@Service
@Slf4j
public class UnifiedPaymentServiceImpl implements UnifiedPaymentService {
    
    @Autowired
    private PayServiceFactory payServiceFactory;
    
    @Autowired
    private PaymentOrderService paymentOrderService;
    
    /**
     * 支付宝支付 - 直接使用pay-java-parent官方实现
     */
    @Override
    public PaymentResultVO createAlipayOrder(CreateOrderDTO orderDTO) {
        try {
            // 1. 获取支付宝官方服务
            PayService payService = payServiceFactory.getPayService("ALIPAY");
            
            // 2. 构建支付宝官方请求对象
            AliPayOrderRequest request = AliPayOrderRequest.builder()
                .subject(orderDTO.getProductName())
                .outTradeNo(orderDTO.getOrderNo())
                .totalAmount(orderDTO.getAmount().toString())
                .body(orderDTO.getDescription())
                .productCode("FAST_INSTANT_TRADE_PAY") // 扫码支付
                .timeoutExpress("15m") // 15分钟超时
                .build();
            
            // 3. 调用官方支付接口
            String payUrl = payService.toPay(request);
            
            // 4. 保存订单到统一订单表
            PaymentOrder order = buildPaymentOrder(orderDTO, "ALIPAY");
            paymentOrderService.save(order);
            
            // 5. 返回支付结果
            return PaymentResultVO.builder()
                .orderNo(orderDTO.getOrderNo())
                .payUrl(payUrl)
                .qrCode(generateQrCode(payUrl))
                .expireTime(LocalDateTime.now().plusMinutes(15))
                .build();
                
        } catch (Exception e) {
            log.error("支付宝支付创建失败: orderNo={}", orderDTO.getOrderNo(), e);
            throw new PaymentException("支付宝支付创建失败: " + e.getMessage());
        }
    }
    
    /**
     * 微信支付 - 直接使用pay-java-parent官方实现
     */
    @Override
    public PaymentResultVO createWechatPayOrder(CreateOrderDTO orderDTO) {
        try {
            // 1. 获取微信支付官方服务
            PayService payService = payServiceFactory.getPayService("WECHAT");
            
            // 2. 构建微信支付官方请求对象
            WxPayOrderRequest request = WxPayOrderRequest.builder()
                .body(orderDTO.getProductName())
                .outTradeNo(orderDTO.getOrderNo())
                .totalFee(orderDTO.getAmount().multiply(new BigDecimal("100")).intValue()) // 转为分
                .spbillCreateIp(orderDTO.getClientIp())
                .tradeType("NATIVE") // 扫码支付
                .timeExpire(DateUtil.format(LocalDateTime.now().plusMinutes(15), "yyyyMMddHHmmss"))
                .build();
            
            // 3. 调用官方支付接口
            String payUrl = payService.toPay(request);
            
            // 4. 保存订单到统一订单表
            PaymentOrder order = buildPaymentOrder(orderDTO, "WECHAT");
            paymentOrderService.save(order);
            
            // 5. 返回支付结果
            return PaymentResultVO.builder()
                .orderNo(orderDTO.getOrderNo())
                .payUrl(payUrl)
                .qrCode(generateQrCode(payUrl))
                .expireTime(LocalDateTime.now().plusMinutes(15))
                .build();
                
        } catch (Exception e) {
            log.error("微信支付创建失败: orderNo={}", orderDTO.getOrderNo(), e);
            throw new PaymentException("微信支付创建失败: " + e.getMessage());
        }
    }
    
    /**
     * 统一支付回调处理 - 使用pay-java-parent官方回调验证
     */
    @Override
    public Boolean handlePaymentCallback(String channelCode, HttpServletRequest request) {
        try {
            // 1. 获取对应的支付服务
            PayService payService = payServiceFactory.getPayService(channelCode);
            
            // 2. 使用官方回调验证
            PayResponse payResponse = payService.callback(request);
            
            if (payResponse.isSuccess()) {
                // 3. 更新订单状态
                String orderNo = payResponse.getOutTradeNo();
                PaymentOrder order = paymentOrderService.getByOrderNo(orderNo);
                
                if (order != null && order.getStatus() == 0) { // 待支付状态
                    // 更新为已支付
                    order.setStatus(1);
                    order.setThirdPartyTransactionId(payResponse.getTradeNo());
                    order.setPayTime(LocalDateTime.now());
                    order.setCallbackTime(LocalDateTime.now());
                    order.setCallbackParams(JSON.toJSONString(payResponse));
                    
                    paymentOrderService.updateById(order);
                    
                    // 4. 执行业务逻辑 (发货、增加积分等)
                    executeBusinessLogic(order);
                    
                    log.info("支付回调处理成功: orderNo={}, transactionId={}", 
                            orderNo, payResponse.getTradeNo());
                    return true;
                }
            }
            
            log.warn("支付回调验证失败: channelCode={}", channelCode);
            return false;
            
        } catch (Exception e) {
            log.error("支付回调处理异常: channelCode={}", channelCode, e);
            return false;
        }
    }
    
    /**
     * 查询支付状态 - 使用pay-java-parent官方查询接口
     */
    @Override
    public PaymentOrderVO queryPaymentOrder(String orderNo) {
        try {
            PaymentOrder order = paymentOrderService.getByOrderNo(orderNo);
            if (order == null) {
                throw new ServiceException("订单不存在: " + orderNo);
            }
            
            // 如果订单未支付，调用官方接口查询最新状态
            if (order.getStatus() == 0) {
                PayService payService = payServiceFactory.getPayService(order.getPaymentChannel());
                
                // 构建查询请求
                QueryOrder queryRequest = QueryOrder.builder()
                    .outTradeNo(orderNo)
                    .build();
                
                // 调用官方查询接口
                Map<String, Object> result = payService.query(queryRequest);
                
                // 根据查询结果更新订单状态
                updateOrderStatusFromQuery(order, result);
            }
            
            return convertToVO(order);
            
        } catch (Exception e) {
            log.error("查询支付订单失败: orderNo={}", orderNo, e);
            throw new ServiceException("查询支付订单失败: " + e.getMessage());
        }
    }
}
```

## 4. 配置管理

### 4.1 数据库配置
支付渠道配置完全存储在数据库中，支持动态修改：

```sql
-- 支付宝配置示例
INSERT INTO payment_channel_config VALUES (
    1, 'ALIPAY', '支付宝', 'SCAN',
    'your_alipay_app_id',                    -- app_id
    'your_alipay_pid',                       -- merchant_id
    NULL,                                    -- app_secret (支付宝不需要)
    'your_alipay_private_key',               -- private_key
    'alipay_public_key',                     -- public_key
    NULL,                                    -- cert_path
    'https://openapi.alipay.com/gateway.do', -- api_url
    'https://yourdomain.com/api/payment/callback/alipay', -- notify_url
    'https://yourdomain.com/payment/return/alipay',       -- return_url
    'RSA2',                                  -- sign_type
    '["CNY","USD"]',                         -- supported_currencies
    1, 1, 'prod', 0.006, 0.01, 50000,
    '{"timeout":"15m","product_code":"FAST_INSTANT_TRADE_PAY"}',
    '支付宝官方配置', 0, NOW(), NOW()
);
```

### 4.2 配置热更新
```java
@Service
public class PaymentConfigService {
    
    @Cacheable(value = "payment_config", key = "#channelCode")
    public PaymentChannelConfig getConfig(String channelCode) {
        return paymentChannelConfigMapper.selectByChannelCode(channelCode);
    }
    
    @CacheEvict(value = "payment_config", key = "#channelCode")
    public void refreshConfig(String channelCode) {
        log.info("刷新支付配置缓存: {}", channelCode);
    }
}
```

## 5. 测试验证

### 5.1 单元测试
```java
@SpringBootTest
class UnifiedPaymentServiceTest {
    
    @Autowired
    private UnifiedPaymentService paymentService;
    
    @Test
    void testCreateAlipayOrder() {
        CreateOrderDTO orderDTO = CreateOrderDTO.builder()
            .orderNo("TEST" + System.currentTimeMillis())
            .productName("测试商品")
            .amount(new BigDecimal("0.01"))
            .description("测试订单")
            .build();
            
        PaymentResultVO result = paymentService.createAlipayOrder(orderDTO);
        
        assertThat(result).isNotNull();
        assertThat(result.getOrderNo()).isEqualTo(orderDTO.getOrderNo());
        assertThat(result.getPayUrl()).isNotBlank();
    }
}
```

### 5.2 集成测试
- ✅ 支付宝沙箱环境测试
- ✅ 微信支付沙箱环境测试  
- ✅ 回调接口测试
- ✅ 订单查询测试
- ✅ 退款接口测试

## 6. 部署说明

### 6.1 环境配置
```bash
# 生产环境配置
export ALIPAY_APP_ID="your_production_app_id"
export ALIPAY_PRIVATE_KEY="your_production_private_key"
export WECHAT_APP_ID="your_production_app_id"
export WECHAT_MCH_ID="your_production_mch_id"
```

### 6.2 证书管理
- 支付宝公钥证书定期更新
- 微信支付证书安全存储
- 证书过期监控和告警

## 7. 监控和运维

### 7.1 关键指标
- 支付成功率 > 99%
- 回调处理成功率 > 99.5%
- 支付接口响应时间 < 3s
- 订单查询响应时间 < 1s

### 7.2 告警配置
- 支付成功率低于阈值告警
- 回调处理失败告警
- 接口响应时间过长告警
- 证书即将过期告警

通过直接使用pay-java-parent官方实现，我们可以快速、安全、稳定地集成支付宝和微信支付，同时保持SE支付和Momo支付的现有实现，实现统一的支付架构。
