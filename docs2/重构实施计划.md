# 超级智能社(SuperAI)重构实施计划

## 1. 重构总体规划

### 1.1 重构原则
- **业务连续性**: 保证现有业务功能100%不受影响
- **渐进式重构**: 分模块、分阶段逐步重构，避免大爆炸式改动
- **向后兼容**: 新旧系统并存期间保持API向后兼容
- **数据安全**: 重构过程中确保数据完整性和安全性
- **性能优化**: 重构的同时提升系统性能和稳定性

### 1.2 重构范围
```yaml
保持不变:
  - 主启动类: ChatgptApplication.java
  - 基础包结构: com.hncboy.chatgpt
  - 核心数据表: 61张表结构保持不变
  - 现有API接口: 保持向后兼容

重构内容:
  - 用户认证系统: 引入JustAuth + Sa-Token
  - 支付系统: 统一为pay-java-parent
  - 缓存架构: Redis + Caffeine多级缓存
  - 国际化支持: 多语种、多币种、多时区
  - AI通道优化: 基于现有三表优化选择算法

新增功能:
  - 多渠道登录: 7种登录方式
  - 统一支付: 4种支付渠道
  - 内容审核: 敏感词过滤
  - 缓存管理: 动态缓存控制
```

## 2. 分阶段实施计划

### 2.1 第一阶段：基础架构重构 (2周)

#### 2.1.1 Week 1: 认证系统重构
**目标**: 完成用户认证系统的重构，支持多种登录方式

**任务清单**:
- [ ] 引入JustAuth和Sa-Token依赖
- [ ] 创建用户联合登录相关表
- [ ] 实现认证提供者抽象层
- [ ] 开发7种登录方式的具体实现
- [ ] 重构现有登录接口，保持向后兼容
- [ ] 编写单元测试和集成测试

**技术实现**:
```java
// 1. 添加依赖
<dependency>
    <groupId>me.zhyd.oauth</groupId>
    <artifactId>JustAuth</artifactId>
    <version>1.16.5</version>
</dependency>
<dependency>
    <groupId>cn.dev33</groupId>
    <artifactId>sa-token-spring-boot-starter</artifactId>
    <version>1.34.0</version>
</dependency>

// 2. 配置Sa-Token
@Configuration
public class SaTokenConfig {
    @Bean
    public StpInterface stpInterface() {
        return new StpInterfaceImpl();
    }
}

// 3. 实现认证提供者
@Component
public class WechatAuthProvider extends AbstractAuthProvider {
    @Override
    public String getLoginType() {
        return "WECHAT";
    }
    
    @Override
    public LoginResultVO authenticate(AuthRequest authRequest) {
        // 微信登录实现
    }
}
```

**验收标准**:
- [ ] 所有7种登录方式正常工作
- [ ] 现有微信登录功能不受影响
- [ ] 新用户可以通过任意方式注册登录
- [ ] 老用户可以绑定新的登录方式

#### 2.1.2 Week 2: 缓存架构升级
**目标**: 建立Redis + Caffeine多级缓存架构

**任务清单**:
- [ ] 配置Redis和Caffeine缓存管理器
- [ ] 实现缓存管理服务
- [ ] 重构现有缓存使用方式
- [ ] 添加缓存监控和统计
- [ ] 实现缓存预热机制
- [ ] 开发缓存管理接口

**技术实现**:
```java
// 1. 多级缓存配置
@Configuration
@EnableCaching
public class CacheConfig {
    
    @Bean
    @Primary
    public CacheManager cacheManager() {
        CompositeCacheManager cacheManager = new CompositeCacheManager();
        cacheManager.setCacheManagers(
            caffeineCacheManager(),
            redisCacheManager()
        );
        return cacheManager;
    }
}

// 2. 缓存管理服务
@Service
public class CacheManagerService {
    public void clearCache(String cacheName) {
        // 清空指定缓存
    }
    
    public void refreshCache(String cacheName, String key) {
        // 刷新指定缓存
    }
}
```

**验收标准**:
- [ ] 缓存命中率提升30%以上
- [ ] 接口响应时间减少20%以上
- [ ] 缓存管理接口正常工作
- [ ] 缓存统计数据准确

### 2.2 第二阶段：支付系统重构 (2周)

#### 2.2.1 Week 3: 统一支付架构 - 直接使用pay-java-parent官方实现
**目标**: 基于pay-java-parent官方API重构支付系统，无需重写现有支付逻辑

**任务清单**:
- [ ] 引入pay-java-parent依赖
- [ ] 创建统一支付订单表和配置表
- [ ] 配置支付宝和微信支付的官方参数
- [ ] 实现统一支付服务，直接调用官方API
- [ ] 保持SE支付和Momo支付现有实现
- [ ] 数据迁移：现有订单数据迁移到新表
- [ ] 重构支付相关接口

**技术实现**:
```java
// 1. 直接使用pay-java-parent官方实现
@Service
public class UnifiedPaymentServiceImpl implements UnifiedPaymentService {

    @Override
    public PaymentResultVO createAlipayOrder(CreateOrderDTO orderDTO) {
        // 1. 获取支付宝配置
        AliPayConfig config = buildAlipayConfig();

        // 2. 创建官方支付服务
        PayService payService = new AliPayServiceImpl(config);

        // 3. 构建官方请求对象
        AliPayOrderRequest request = AliPayOrderRequest.builder()
            .subject(orderDTO.getProductName())
            .outTradeNo(orderDTO.getOrderNo())
            .totalAmount(orderDTO.getAmount().toString())
            .build();

        // 4. 调用官方支付接口
        String payUrl = payService.toPay(request);

        return PaymentResultVO.builder()
            .orderNo(orderDTO.getOrderNo())
            .payUrl(payUrl)
            .build();
    }

    @Override
    public PaymentResultVO createWechatPayOrder(CreateOrderDTO orderDTO) {
        // 同样直接使用微信支付官方实现
        WxPayConfig config = buildWechatPayConfig();
        PayService payService = new WxPayServiceImpl(config);
        // ... 官方API调用
    }
}

// 2. 支付配置构建器
@Component
public class PaymentConfigBuilder {

    public AliPayConfig buildAlipayConfig() {
        PaymentChannelConfig config = getChannelConfig("ALIPAY");
        return AliPayConfig.builder()
            .appId(config.getAppId())
            .privateKey(config.getPrivateKey())
            .publicKey(config.getPublicKey())
            .serverUrl(config.getApiUrl())
            .signType("RSA2")
            .build();
    }
}
```

**验收标准**:
- [ ] 支付宝和微信支付使用官方API，无自研代码
- [ ] SE支付和Momo支付保持现有实现不变
- [ ] 现有支付功能100%兼容
- [ ] 支付成功率保持99%以上
- [ ] 支付回调处理正常
- [ ] 配置参数从数据库动态读取

#### 2.2.2 Week 4: 多币种支持
**目标**: 实现多币种价格显示和支付

**任务清单**:
- [ ] 创建汇率配置表
- [ ] 实现汇率服务
- [ ] 重构产品价格显示逻辑
- [ ] 添加币种转换功能
- [ ] 更新支付接口支持多币种
- [ ] 添加汇率定时更新任务

**验收标准**:
- [ ] 支持CNY、USD、VND三种币种
- [ ] 价格显示根据用户偏好自动转换
- [ ] 汇率数据实时更新
- [ ] 支付金额计算准确

### 2.3 第三阶段：AI通道优化 (1周)

#### 2.3.1 Week 5: 通道选择算法优化
**目标**: 基于现有model、channel_config、site_info三表优化通道选择

**任务清单**:
- [ ] 分析现有通道选择逻辑
- [ ] 实现多因素评分算法
- [ ] 添加故障转移机制
- [ ] 实现健康检查定时任务
- [ ] 添加通道使用统计
- [ ] 优化缓存策略

**技术实现**:
```java
@Service
public class OptimizedChannelSelectorService {
    
    @Cacheable(value = "optimal_channel", key = "#modelId")
    public ChannelInfo selectOptimalChannel(String modelId, ChatRequest request) {
        // 1. 获取所有可用通道
        List<ChannelConfig> channels = getAvailableChannels(modelId);
        
        // 2. 多因素评分
        ChannelConfig selected = selectByAdvancedScore(channels, request);
        
        // 3. 构建通道信息
        return buildChannelInfo(selected);
    }
    
    private double calculateAdvancedScore(ChannelConfig channel, ChatRequest request) {
        // 优先级(30%) + 成本(25%) + 性能(20%) + 可用性(15%) + 负载(10%)
        return priorityScore * 0.3 + costScore * 0.25 + performanceScore * 0.2 
             + availabilityScore * 0.15 + loadScore * 0.1;
    }
}
```

**验收标准**:
- [ ] 通道选择响应时间<100ms
- [ ] 故障转移成功率>99%
- [ ] AI对话成功率提升5%
- [ ] 通道负载均衡有效

### 2.4 第四阶段：功能完善 (1周)

#### 2.4.1 Week 6: 国际化注解翻译和Spring AI集成
**目标**: 完成数据库驱动的国际化注解翻译系统和Spring AI集成

**任务清单**:
- [ ] 实现国际化注解翻译系统 (参考RuoYi-Vue-Plus)
- [ ] 创建Jackson序列化处理器，支持{{param}}参数化翻译
- [ ] 集成Spring AI框架，替换部分AI功能
- [ ] 实现向量搜索和RAG功能
- [ ] 添加AI Agent和函数调用支持
- [ ] 完善系统监控和性能测试

**技术实现**:
```java
// 1. 国际化注解翻译
@Data
public class UserInfoVO {
    @I18nTranslation(key = "user.points.message", paramFields = {"points"})
    private String pointsMessage; // "您有{{points}}个积分"

    private Integer points;
}

// 2. Spring AI集成
@Service
public class SpringAIChatService {
    @Autowired
    private ChatClient chatClient;

    public ChatResponse chat(ChatRequest request) {
        return chatClient.prompt()
            .user(request.getContent())
            .call()
            .chatResponse();
    }
}
```

**验收标准**:
- [ ] 国际化注解翻译正常工作，支持参数化
- [ ] 支持中文、英文、越南语三种语言
- [ ] Spring AI ChatClient正常工作
- [ ] 向量搜索和RAG功能正常
- [ ] AI Agent函数调用正常
- [ ] 系统整体性能提升20%

## 3. 风险控制措施

### 3.1 数据安全保障
```bash
# 1. 数据备份策略
# 每日全量备份
mysqldump --single-transaction --routines --triggers super_gpt > backup_$(date +%Y%m%d).sql

# 2. 增量备份
mysqlbinlog --start-datetime="2024-01-01 00:00:00" mysql-bin.000001 > incremental_backup.sql

# 3. 数据验证
# 重构前后数据一致性检查
SELECT COUNT(*) FROM user_base_info; -- 重构前后对比
```

### 3.2 灰度发布策略
```yaml
灰度发布计划:
  阶段1: 内部测试 (1%)
    - 开发团队使用新功能
    - 验证基本功能正常
    
  阶段2: 小范围测试 (5%)
    - 选择活跃用户进行测试
    - 收集用户反馈
    
  阶段3: 扩大范围 (20%)
    - 逐步扩大用户范围
    - 监控系统稳定性
    
  阶段4: 全量发布 (100%)
    - 全部用户使用新功能
    - 持续监控和优化
```

### 3.3 回滚方案
```yaml
回滚触发条件:
  - 系统错误率 > 1%
  - 响应时间增加 > 50%
  - 用户投诉量激增
  - 数据不一致

回滚步骤:
  1. 立即切换到备用系统
  2. 恢复数据库到最近备份点
  3. 回滚代码到上一个稳定版本
  4. 通知相关人员
  5. 分析问题原因
```

### 3.4 监控告警
```yaml
监控指标:
  系统指标:
    - CPU使用率 < 80%
    - 内存使用率 < 85%
    - 磁盘使用率 < 90%
    
  业务指标:
    - 接口响应时间 < 2s
    - 接口成功率 > 99%
    - 用户登录成功率 > 98%
    - 支付成功率 > 99%
    
  告警方式:
    - 邮件通知
    - 短信通知
    - 钉钉群通知
```

## 4. 测试策略

### 4.1 测试类型
- **单元测试**: 覆盖率>80%
- **集成测试**: 关键业务流程测试
- **性能测试**: 压力测试和负载测试
- **安全测试**: 认证授权和数据安全测试
- **兼容性测试**: 新旧系统兼容性测试

### 4.2 测试环境
- **开发环境**: 开发人员日常测试
- **测试环境**: QA团队功能测试
- **预生产环境**: 生产环境镜像，最终验证
- **生产环境**: 灰度发布和监控

## 5. 项目交付物

### 5.1 代码交付物
- [ ] 重构后的完整源代码
- [ ] 数据库迁移脚本
- [ ] 配置文件模板
- [ ] 部署脚本

### 5.2 文档交付物
- [ ] 重构技术方案文档
- [ ] API接口文档
- [ ] 数据库设计文档
- [ ] 部署运维文档
- [ ] 用户使用手册

### 5.3 测试交付物
- [ ] 测试用例文档
- [ ] 测试报告
- [ ] 性能测试报告
- [ ] 安全测试报告

## 6. 成功标准

### 6.1 技术指标
- [ ] 系统可用性 > 99.9%
- [ ] 接口响应时间减少 20%
- [ ] 缓存命中率提升 30%
- [ ] 代码覆盖率 > 80%

### 6.2 业务指标
- [ ] 用户登录成功率 > 98%
- [ ] 支付成功率 > 99%
- [ ] AI对话成功率 > 95%
- [ ] 用户满意度 > 90%

### 6.3 运维指标
- [ ] 部署时间减少 50%
- [ ] 故障恢复时间 < 5分钟
- [ ] 监控覆盖率 100%
- [ ] 自动化程度 > 90%
