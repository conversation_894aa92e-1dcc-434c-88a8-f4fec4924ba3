# 超级智能社(SuperAI)重构版需求详细设计说明书

## 1. 用户模块详细设计

### 1.1 包结构设计 (基于现有com.hncboy.chatgpt重构)

```
com.hncboy.chatgpt.api
├── controller
│   ├── UserController.java                    # 用户管理控制器 (重构)
│   ├── AuthController.java                    # 用户认证控制器 (重构)
│   └── UserProfileController.java             # 用户资料控制器 (新增)
├── service
│   ├── user
│   │   ├── UserService.java                   # 用户服务接口 (重构)
│   │   ├── UserAuthService.java               # 用户认证服务接口 (重构)
│   │   ├── UserJointLoginService.java         # 联合登录服务接口 (新增)
│   │   └── impl
│   │       ├── UserServiceImpl.java           # 用户服务实现 (重构)
│   │       ├── UserAuthServiceImpl.java       # 用户认证服务实现 (重构)
│   │       └── UserJointLoginServiceImpl.java # 联合登录服务实现 (新增)
├── mapper
│   ├── UserBaseInfoMapper.java                # 用户基础信息数据访问 (基于现有表)
│   ├── UsersMapper.java                       # 第三方用户数据访问 (基于现有表)
│   ├── UserJointLoginMapper.java              # 联合登录数据访问 (新增表)
│   └── UserJointConfigMapper.java             # 联合登录配置数据访问 (新增表)
├── domain
│   ├── entity
│   │   ├── UserBaseInfo.java                  # 用户基础信息实体 (基于现有表)
│   │   ├── Users.java                         # 第三方用户实体 (基于现有表)
│   │   ├── UserJointLogin.java                # 联合登录实体 (新增)
│   │   └── UserJointConfig.java               # 联合登录配置实体 (新增)
│   ├── dto
│   │   ├── UserRegisterDTO.java               # 用户注册DTO
│   │   ├── UserLoginDTO.java                  # 用户登录DTO
│   │   ├── UserUpdateDTO.java                 # 用户更新DTO
│   │   └── UserDeactivateDTO.java             # 用户注销DTO
│   └── vo
│       ├── UserInfoVO.java                    # 用户信息VO
│       ├── UserProfileVO.java                 # 用户资料VO
│       └── LoginResultVO.java                 # 登录结果VO

com.hncboy.chatgpt.framework.auth              # 认证框架 (新增)
├── provider
│   ├── AbstractAuthProvider.java              # 抽象认证提供者
│   ├── WechatAuthProvider.java                # 微信认证提供者
│   ├── GoogleAuthProvider.java                # Google认证提供者
│   ├── FacebookAuthProvider.java              # Facebook认证提供者
│   ├── PhoneAuthProvider.java                 # 手机号认证提供者
│   ├── EmailAuthProvider.java                 # 邮箱认证提供者
│   ├── PasswordAuthProvider.java              # 密码认证提供者
│   └── FingerprintAuthProvider.java           # 指纹认证提供者
├── handler
│   ├── AuthSuccessHandler.java                # 认证成功处理器
│   ├── AuthFailureHandler.java                # 认证失败处理器
│   └── AuthCallbackHandler.java               # 认证回调处理器
└── config
    ├── JustAuthConfig.java                    # JustAuth配置
    ├── SaTokenConfig.java                     # Sa-Token配置
    └── AuthProviderConfig.java               # 认证提供者配置
```

### 1.2 核心类设计

#### 1.2.1 用户基础信息实体类 (UserBaseInfo.java) - 基于现有表重构
```java
@Data
@TableName("user_base_info")
@ApiModel("用户基础信息实体")
public class UserBaseInfo {
    @TableId(type = IdType.AUTO)
    @ApiModelProperty("用户ID")
    private Integer id;
    
    @ApiModelProperty("分佣身份ID")
    private Integer commissionId;

    @ApiModelProperty("账号(手机号或其他账号)")
    private String account;

    @ApiModelProperty("用户名")
    private String name;

    @ApiModelProperty("昵称")
    private String nickName;

    @ApiModelProperty("密码(BCrypt加密)")
    private String password;

    @ApiModelProperty("最后登录时间")
    private LocalDateTime loginTime;

    @ApiModelProperty("IP地址")
    private String ip;

    @ApiModelProperty("头像URL")
    private String headSculpture;

    @ApiModelProperty("地址")
    private String address;

    @ApiModelProperty("邮箱")
    private String email;

    @ApiModelProperty("状态(0:正常 1:禁用)")
    private Integer status;

    @ApiModelProperty("剩余可用次数(充值)")
    private Integer useNum;

    @ApiModelProperty("免费可用次数(赠送)")
    private Integer freeNum;

    @ApiModelProperty("每日免费次数")
    private Integer dailyFreeTime;

    @ApiModelProperty("绘画次数")
    private Integer drawNum;

    @ApiModelProperty("音乐创作次数")
    private Integer musicNum;

    @ApiModelProperty("写作次数")
    private Integer writeNum;

    @ApiModelProperty("VIP到期时间")
    private LocalDateTime vipEndTime;

    @ApiModelProperty("邀请人ID")
    private String inviteUserId;

    @ApiModelProperty("关联users表ID")
    private Long usersId;
    
    @ApiModelProperty("是否删除(0:否 1:是)")
    @TableLogic
    private Integer deleted;
    
    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}
```

#### 1.2.2 联合登录实体类 (UserJointLogin.java)
```java
@Data
@TableName("user_joint_login")
@ApiModel("用户联合登录实体")
public class UserJointLogin {
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty("主键ID")
    private Long id;
    
    @ApiModelProperty("用户ID")
    private Long userId;
    
    @ApiModelProperty("登录类型(WECHAT/GOOGLE/FACEBOOK/PHONE/EMAIL/PASSWORD/FINGERPRINT)")
    private String loginType;
    
    @ApiModelProperty("第三方唯一标识")
    private String thirdPartyId;
    
    @ApiModelProperty("第三方用户名")
    private String thirdPartyUsername;
    
    @ApiModelProperty("第三方邮箱")
    private String thirdPartyEmail;
    
    @ApiModelProperty("第三方头像")
    private String thirdPartyAvatar;
    
    @ApiModelProperty("访问令牌")
    private String accessToken;
    
    @ApiModelProperty("刷新令牌")
    private String refreshToken;
    
    @ApiModelProperty("令牌过期时间")
    private LocalDateTime tokenExpireTime;
    
    @ApiModelProperty("扩展信息(JSON格式)")
    private String extraInfo;
    
    @ApiModelProperty("是否主要登录方式(0:否 1:是)")
    private Integer isPrimary;
    
    @ApiModelProperty("状态(0:正常 1:禁用)")
    private Integer status;
    
    @ApiModelProperty("绑定时间")
    private LocalDateTime bindTime;
    
    @ApiModelProperty("最后使用时间")
    private LocalDateTime lastUseTime;
    
    @ApiModelProperty("是否删除(0:否 1:是)")
    @TableLogic
    private Integer deleted;
    
    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}
```

#### 1.2.3 联合登录配置实体类 (UserJointConfig.java)
```java
@Data
@TableName("user_joint_config")
@ApiModel("用户联合登录配置实体")
public class UserJointConfig {
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty("主键ID")
    private Long id;
    
    @ApiModelProperty("登录类型")
    private String loginType;
    
    @ApiModelProperty("配置名称")
    private String configName;
    
    @ApiModelProperty("客户端ID")
    private String clientId;
    
    @ApiModelProperty("客户端密钥")
    private String clientSecret;
    
    @ApiModelProperty("回调地址")
    private String redirectUri;
    
    @ApiModelProperty("授权范围")
    private String scope;
    
    @ApiModelProperty("配置参数(JSON格式)")
    private String configParams;
    
    @ApiModelProperty("是否启用(0:否 1:是)")
    private Integer enabled;
    
    @ApiModelProperty("排序权重")
    private Integer sortOrder;
    
    @ApiModelProperty("适用环境(dev/test/prod)")
    private String environment;
    
    @ApiModelProperty("备注")
    private String remark;
    
    @ApiModelProperty("是否删除(0:否 1:是)")
    @TableLogic
    private Integer deleted;
    
    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}
```

### 1.3 核心服务接口设计

#### 1.3.1 用户服务接口 (UserService.java)
```java
public interface UserService extends IService<User> {
    
    /**
     * 根据用户ID获取用户信息
     */
    UserInfoVO getUserInfo(Long userId);
    
    /**
     * 更新用户资料
     */
    Boolean updateUserProfile(Long userId, UserUpdateDTO updateDTO);
    
    /**
     * 用户注销
     */
    Boolean deactivateUser(Long userId, UserDeactivateDTO deactivateDTO);
    
    /**
     * 检查用户是否存在
     */
    Boolean checkUserExists(String identifier, String identifierType);
    
    /**
     * 根据第三方信息查找用户
     */
    User findUserByThirdParty(String loginType, String thirdPartyId);
    
    /**
     * 创建新用户
     */
    User createUser(UserRegisterDTO registerDTO);
    
    /**
     * 更新用户最后登录信息
     */
    void updateLastLoginInfo(Long userId, String loginIp);
    
    /**
     * 获取用户统计信息
     */
    Map<String, Object> getUserStats(Long userId);
}
```

#### 1.3.2 用户认证服务接口 (UserAuthService.java)
```java
public interface UserAuthService {
    
    /**
     * 用户登录
     */
    LoginResultVO login(UserLoginDTO loginDTO);
    
    /**
     * 第三方登录
     */
    LoginResultVO thirdPartyLogin(String loginType, String authCode, String state);
    
    /**
     * 用户注册
     */
    LoginResultVO register(UserRegisterDTO registerDTO);
    
    /**
     * 发送验证码
     */
    Boolean sendVerificationCode(String target, String type);
    
    /**
     * 验证验证码
     */
    Boolean verifyCode(String target, String code, String type);
    
    /**
     * 刷新令牌
     */
    LoginResultVO refreshToken(String refreshToken);
    
    /**
     * 用户登出
     */
    Boolean logout(Long userId);
    
    /**
     * 绑定第三方账号
     */
    Boolean bindThirdPartyAccount(Long userId, String loginType, String authCode);
    
    /**
     * 解绑第三方账号
     */
    Boolean unbindThirdPartyAccount(Long userId, String loginType);
}
```

### 1.4 控制器接口设计

#### 1.4.1 用户认证控制器 (AuthController.java) - 基于现有结构重构
```java
@RestController
@RequestMapping("/api/auth")
@Api(tags = "用户认证接口")
@Slf4j
public class AuthController {

    @Autowired
    private UserAuthService userAuthService;

    @PostMapping("/login")
    @ApiOperation("用户登录")
    public BaseResponse<LoginResultVO> login(@Valid @RequestBody UserLoginDTO loginDTO) {
        LoginResultVO result = userAuthService.login(loginDTO);
        return BaseResponse.success(result);
    }

    @PostMapping("/register")
    @ApiOperation("用户注册")
    public BaseResponse<LoginResultVO> register(@Valid @RequestBody UserRegisterDTO registerDTO) {
        LoginResultVO result = userAuthService.register(registerDTO);
        return BaseResponse.success(result);
    }

    @GetMapping("/oauth/{loginType}/authorize")
    @ApiOperation("第三方登录授权URL")
    public BaseResponse<String> getOAuthAuthorizeUrl(@PathVariable String loginType,
                                                    @RequestParam(required = false) String state) {
        String authorizeUrl = userAuthService.getOAuthAuthorizeUrl(loginType, state);
        return BaseResponse.success(authorizeUrl);
    }

    @PostMapping("/oauth/{loginType}/callback")
    @ApiOperation("第三方登录回调")
    public BaseResponse<LoginResultVO> oauthCallback(@PathVariable String loginType,
                                                    @RequestParam String code,
                                                    @RequestParam(required = false) String state) {
        LoginResultVO result = userAuthService.handleOAuthCallback(loginType, code, state);
        return BaseResponse.success(result);
    }

    @PostMapping("/send-verification-code")
    @ApiOperation("发送验证码")
    public BaseResponse<Boolean> sendVerificationCode(@Valid @RequestBody SendCodeDTO sendCodeDTO) {
        Boolean result = userAuthService.sendVerificationCode(sendCodeDTO.getTarget(), sendCodeDTO.getType());
        return BaseResponse.success(result);
    }

    @PostMapping("/verify-code")
    @ApiOperation("验证验证码")
    public BaseResponse<Boolean> verifyCode(@Valid @RequestBody VerifyCodeDTO verifyCodeDTO) {
        Boolean result = userAuthService.verifyCode(verifyCodeDTO.getTarget(),
                                                   verifyCodeDTO.getCode(),
                                                   verifyCodeDTO.getType());
        return BaseResponse.success(result);
    }

    @PostMapping("/refresh-token")
    @ApiOperation("刷新令牌")
    public BaseResponse<LoginResultVO> refreshToken(@Valid @RequestBody RefreshTokenDTO refreshTokenDTO) {
        LoginResultVO result = userAuthService.refreshToken(refreshTokenDTO.getRefreshToken());
        return BaseResponse.success(result);
    }

    @PostMapping("/logout")
    @ApiOperation("用户登出")
    @SaCheckLogin
    public BaseResponse<Boolean> logout() {
        Integer userId = StpUtil.getLoginIdAsInt();
        Boolean result = userAuthService.logout(userId);
        return BaseResponse.success(result);
    }

    @GetMapping("/login-configs")
    @ApiOperation("获取可用登录方式配置")
    public BaseResponse<List<LoginConfigVO>> getLoginConfigs() {
        List<LoginConfigVO> configs = userAuthService.getAvailableLoginConfigs();
        return BaseResponse.success(configs);
    }
}
```

#### 1.4.2 用户管理控制器 (UserController.java) - 基于现有结构重构
```java
@RestController
@RequestMapping("/api/user")
@Api(tags = "用户管理接口")
@SaCheckLogin
@Slf4j
public class UserController {

    @Autowired
    private UserService userService;

    @GetMapping("/info")
    @ApiOperation("获取用户信息")
    public BaseResponse<UserInfoVO> getUserInfo() {
        Integer userId = StpUtil.getLoginIdAsInt();
        UserInfoVO userInfo = userService.getUserInfo(userId);
        return BaseResponse.success(userInfo);
    }

    @PutMapping("/profile")
    @ApiOperation("更新用户资料")
    public BaseResponse<Boolean> updateProfile(@Valid @RequestBody UserUpdateDTO updateDTO) {
        Integer userId = StpUtil.getLoginIdAsInt();
        Boolean result = userService.updateUserProfile(userId, updateDTO);
        return BaseResponse.success(result);
    }

    @PostMapping("/deactivate")
    @ApiOperation("用户注销")
    public BaseResponse<Boolean> deactivateAccount(@Valid @RequestBody UserDeactivateDTO deactivateDTO) {
        Integer userId = StpUtil.getLoginIdAsInt();
        Boolean result = userService.deactivateUser(userId, deactivateDTO);
        return BaseResponse.success(result);
    }

    @GetMapping("/joint-logins")
    @ApiOperation("获取绑定的第三方账号")
    public BaseResponse<List<UserJointLoginVO>> getJointLogins() {
        Integer userId = StpUtil.getLoginIdAsInt();
        List<UserJointLoginVO> jointLogins = userService.getUserJointLogins(userId);
        return BaseResponse.success(jointLogins);
    }

    @PostMapping("/bind-account")
    @ApiOperation("绑定第三方账号")
    public BaseResponse<Boolean> bindAccount(@Valid @RequestBody BindAccountDTO bindAccountDTO) {
        Integer userId = StpUtil.getLoginIdAsInt();
        Boolean result = userService.bindThirdPartyAccount(userId, bindAccountDTO);
        return BaseResponse.success(result);
    }

    @DeleteMapping("/unbind-account/{loginType}")
    @ApiOperation("解绑第三方账号")
    public BaseResponse<Boolean> unbindAccount(@PathVariable String loginType) {
        Integer userId = StpUtil.getLoginIdAsInt();
        Boolean result = userService.unbindThirdPartyAccount(userId, loginType);
        return BaseResponse.success(result);
    }

    @GetMapping("/stats")
    @ApiOperation("获取用户统计信息")
    public BaseResponse<Map<String, Object>> getUserStats() {
        Integer userId = StpUtil.getLoginIdAsInt();
        Map<String, Object> stats = userService.getUserStats(userId);
        return BaseResponse.success(stats);
    }

    @PostMapping("/change-password")
    @ApiOperation("修改密码")
    public BaseResponse<Boolean> changePassword(@Valid @RequestBody ChangePasswordDTO changePasswordDTO) {
        Integer userId = StpUtil.getLoginIdAsInt();
        Boolean result = userService.changePassword(userId, changePasswordDTO);
        return BaseResponse.success(result);
    }

    @GetMapping("/points-log")
    @ApiOperation("获取积分记录")
    public BaseResponse<PageResult<UserPointsLogVO>> getPointsLog(@RequestParam(defaultValue = "1") Integer pageNum,
                                                                 @RequestParam(defaultValue = "10") Integer pageSize) {
        Integer userId = StpUtil.getLoginIdAsInt();
        PageResult<UserPointsLogVO> result = userService.getUserPointsLog(userId, pageNum, pageSize);
        return BaseResponse.success(result);
    }
}
```

## 2. 支付模块详细设计

### 2.1 包结构设计 - 基于pay-java-parent官方实现

```
com.hncboy.chatgpt.api.service.payment        # 支付服务 (基于现有结构)
├── UnifiedPaymentService.java                # 统一支付服务 (新增)
├── PaymentChannelConfigService.java          # 支付渠道配置服务 (新增)
├── PaymentOrderService.java                  # 支付订单服务 (重构现有)
└── impl
    ├── UnifiedPaymentServiceImpl.java        # 统一支付服务实现
    ├── PaymentChannelConfigServiceImpl.java  # 支付渠道配置服务实现
    └── PaymentOrderServiceImpl.java          # 支付订单服务实现

com.hncboy.chatgpt.api.controller             # 控制器 (基于现有结构)
├── PaymentController.java                    # 支付控制器 (重构现有)
└── PaymentCallbackController.java            # 支付回调控制器 (重构现有)

com.hncboy.chatgpt.framework.payment          # 支付框架 (新增)
├── config
│   ├── PayJavaConfig.java                    # pay-java-parent配置
│   ├── AlipayConfigBuilder.java              # 支付宝配置构建器
│   ├── WechatPayConfigBuilder.java           # 微信支付配置构建器
│   ├── SePayConfigBuilder.java               # SE支付配置构建器 (自定义)
│   └── MomoPayConfigBuilder.java             # Momo支付配置构建器 (自定义)
├── factory
│   └── PayServiceFactory.java                # 支付服务工厂
├── handler
│   ├── PaymentCallbackHandler.java           # 统一回调处理器
│   ├── AlipayCallbackHandler.java            # 支付宝回调处理器
│   ├── WechatCallbackHandler.java            # 微信回调处理器
│   ├── SePayCallbackHandler.java             # SE支付回调处理器
│   └── MomoCallbackHandler.java              # Momo回调处理器
└── channel
    ├── OfficialPaymentChannel.java           # 官方支付渠道 (支付宝/微信)
    ├── CustomPaymentChannel.java             # 自定义支付渠道 (SE/Momo)
    └── PaymentChannelRegistry.java           # 支付渠道注册器

com.hncboy.chatgpt.api.domain                 # 领域对象 (基于现有结构)
├── entity
│   ├── PaymentOrder.java                     # 统一支付订单实体 (新增)
│   ├── PaymentChannelConfig.java             # 支付渠道配置实体 (新增)
│   └── Product.java                          # 产品实体 (保持现有)
├── dto
│   ├── CreateOrderDTO.java                   # 创建订单DTO
│   ├── PaymentCallbackDTO.java               # 支付回调DTO
│   └── PaymentQueryDTO.java                  # 支付查询DTO
└── vo
    ├── PaymentOrderVO.java                    # 支付订单VO
    ├── PaymentResultVO.java                   # 支付结果VO
    └── PaymentChannelVO.java                  # 支付渠道VO
```

**重构说明**:
1. **直接使用官方实现**: 支付宝和微信支付直接使用pay-java-parent官方API
2. **保持现有结构**: 在现有com.hncboy.chatgpt包结构下重构
3. **统一接口**: 通过UnifiedPaymentService提供统一的支付接口
4. **配置驱动**: 通过数据库配置驱动不同支付渠道
5. **扩展性**: 支持SE支付和Momo支付的自定义实现

### 2.2 核心类设计

#### 2.2.1 支付订单实体类 (PaymentOrder.java)
```java
@Data
@TableName("payment_order")
@ApiModel("支付订单实体")
public class PaymentOrder {
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty("订单ID")
    private Long id;
    
    @ApiModelProperty("订单号")
    private String orderNo;
    
    @ApiModelProperty("用户ID")
    private Long userId;
    
    @ApiModelProperty("产品ID")
    private Long productId;
    
    @ApiModelProperty("产品名称")
    private String productName;
    
    @ApiModelProperty("产品类型")
    private String productType;
    
    @ApiModelProperty("支付渠道")
    private String paymentChannel;
    
    @ApiModelProperty("支付方式")
    private String paymentMethod;
    
    @ApiModelProperty("订单金额")
    private BigDecimal amount;
    
    @ApiModelProperty("实付金额")
    private BigDecimal paidAmount;
    
    @ApiModelProperty("币种")
    private String currency;
    
    @ApiModelProperty("汇率")
    private BigDecimal exchangeRate;
    
    @ApiModelProperty("订单状态(0:待支付 1:已支付 2:已取消 3:已退款)")
    private Integer status;
    
    @ApiModelProperty("第三方订单号")
    private String thirdPartyOrderNo;
    
    @ApiModelProperty("第三方交易号")
    private String thirdPartyTransactionId;
    
    @ApiModelProperty("支付时间")
    private LocalDateTime payTime;
    
    @ApiModelProperty("过期时间")
    private LocalDateTime expireTime;
    
    @ApiModelProperty("回调时间")
    private LocalDateTime callbackTime;
    
    @ApiModelProperty("支付IP")
    private String paymentIp;
    
    @ApiModelProperty("支付参数(JSON格式)")
    private String paymentParams;
    
    @ApiModelProperty("回调参数(JSON格式)")
    private String callbackParams;
    
    @ApiModelProperty("失败原因")
    private String failureReason;
    
    @ApiModelProperty("备注")
    private String remark;
    
    @ApiModelProperty("是否删除(0:否 1:是)")
    @TableLogic
    private Integer deleted;
    
    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}
```

#### 2.2.2 支付渠道配置实体类 (PaymentChannelConfig.java)
```java
@Data
@TableName("payment_channel_config")
@ApiModel("支付渠道配置实体")
public class PaymentChannelConfig {
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty("主键ID")
    private Long id;
    
    @ApiModelProperty("渠道代码")
    private String channelCode;
    
    @ApiModelProperty("渠道名称")
    private String channelName;
    
    @ApiModelProperty("支付方式")
    private String paymentMethod;
    
    @ApiModelProperty("商户号")
    private String merchantId;
    
    @ApiModelProperty("应用ID")
    private String appId;
    
    @ApiModelProperty("应用密钥")
    private String appSecret;
    
    @ApiModelProperty("公钥")
    private String publicKey;
    
    @ApiModelProperty("私钥")
    private String privateKey;
    
    @ApiModelProperty("API地址")
    private String apiUrl;
    
    @ApiModelProperty("回调地址")
    private String notifyUrl;
    
    @ApiModelProperty("返回地址")
    private String returnUrl;
    
    @ApiModelProperty("支持币种(JSON数组)")
    private String supportedCurrencies;
    
    @ApiModelProperty("配置参数(JSON格式)")
    private String configParams;
    
    @ApiModelProperty("是否启用(0:否 1:是)")
    private Integer enabled;
    
    @ApiModelProperty("排序权重")
    private Integer sortOrder;
    
    @ApiModelProperty("适用环境(dev/test/prod)")
    private String environment;
    
    @ApiModelProperty("费率")
    private BigDecimal feeRate;
    
    @ApiModelProperty("最小金额")
    private BigDecimal minAmount;
    
    @ApiModelProperty("最大金额")
    private BigDecimal maxAmount;
    
    @ApiModelProperty("备注")
    private String remark;
    
    @ApiModelProperty("是否删除(0:否 1:是)")
    @TableLogic
    private Integer deleted;
    
    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}
```

### 2.3 核心服务接口设计 - 基于pay-java-parent

#### 2.3.1 统一支付服务接口 (UnifiedPaymentService.java)
```java
public interface UnifiedPaymentService {

    /**
     * 创建支付订单 - 自动选择最优支付渠道
     */
    PaymentResultVO createPaymentOrder(CreateOrderDTO createOrderDTO);

    /**
     * 支付宝支付 - 直接使用pay-java-parent官方实现
     */
    PaymentResultVO createAlipayOrder(CreateOrderDTO createOrderDTO);

    /**
     * 微信支付 - 直接使用pay-java-parent官方实现
     */
    PaymentResultVO createWechatPayOrder(CreateOrderDTO createOrderDTO);

    /**
     * SE支付 - 保持现有实现
     */
    PaymentResultVO createSePayOrder(CreateOrderDTO createOrderDTO);

    /**
     * Momo支付 - 保持现有实现
     */
    PaymentResultVO createMomoPayOrder(CreateOrderDTO createOrderDTO);

    /**
     * 查询支付订单状态
     */
    PaymentOrderVO queryPaymentOrder(String orderNo);

    /**
     * 统一支付回调处理 - 支持所有渠道
     */
    Boolean handlePaymentCallback(String channelCode, HttpServletRequest request);

    /**
     * 取消支付订单
     */
    Boolean cancelPaymentOrder(String orderNo);

    /**
     * 申请退款 - 使用pay-java-parent官方退款接口
     */
    Boolean refundPaymentOrder(String orderNo, BigDecimal refundAmount, String reason);

    /**
     * 获取可用支付渠道 - 根据币种和金额筛选
     */
    List<PaymentChannelVO> getAvailablePaymentChannels(String currency, BigDecimal amount);

    /**
     * 同步支付状态 - 使用pay-java-parent官方查询接口
     */
    Boolean syncPaymentStatus(String orderNo);

    /**
     * 获取支付统计
     */
    Map<String, Object> getPaymentStats(PaymentQueryDTO queryDTO);
}
```

#### 2.3.2 支付服务工厂 (PayServiceFactory.java)
```java
@Component
@Slf4j
public class PayServiceFactory {

    @Autowired
    private PaymentChannelConfigService channelConfigService;

    /**
     * 获取支付服务 - 基于pay-java-parent官方实现
     */
    public PayService getPayService(String channelCode) {
        PaymentChannelConfig config = channelConfigService.getByChannelCode(channelCode);

        switch (channelCode.toUpperCase()) {
            case "ALIPAY":
                return createAlipayService(config);
            case "WECHAT":
                return createWechatPayService(config);
            default:
                throw new ServiceException("不支持的支付渠道: " + channelCode);
        }
    }

    /**
     * 创建支付宝支付服务 - 使用pay-java-parent官方配置
     */
    private PayService createAlipayService(PaymentChannelConfig config) {
        AliPayConfig aliPayConfig = AliPayConfig.builder()
            .appId(config.getAppId())
            .privateKey(config.getPrivateKey())
            .publicKey(config.getPublicKey())
            .serverUrl(config.getApiUrl())
            .signType(config.getSignType())
            .build();

        return new AliPayServiceImpl(aliPayConfig);
    }

    /**
     * 创建微信支付服务 - 使用pay-java-parent官方配置
     */
    private PayService createWechatPayService(PaymentChannelConfig config) {
        WxPayConfig wxPayConfig = WxPayConfig.builder()
            .appId(config.getAppId())
            .mchId(config.getMerchantId())
            .mchKey(config.getAppSecret())
            .certPath(config.getCertPath())
            .notifyUrl(config.getNotifyUrl())
            .build();

        return new WxPayServiceImpl(wxPayConfig);
    }
}
```

### 2.4 抽象支付渠道设计

#### 2.4.1 抽象支付渠道 (AbstractPaymentChannel.java)
```java
public abstract class AbstractPaymentChannel {
    
    /**
     * 渠道代码
     */
    public abstract String getChannelCode();
    
    /**
     * 渠道名称
     */
    public abstract String getChannelName();
    
    /**
     * 创建支付订单
     */
    public abstract PaymentResultVO createOrder(PaymentOrder order, PaymentChannelConfig config);
    
    /**
     * 查询支付状态
     */
    public abstract PaymentStatusVO queryStatus(String orderNo, PaymentChannelConfig config);
    
    /**
     * 处理支付回调
     */
    public abstract Boolean handleCallback(Map<String, Object> callbackData, PaymentChannelConfig config);
    
    /**
     * 申请退款
     */
    public abstract Boolean refund(String orderNo, BigDecimal amount, PaymentChannelConfig config);
    
    /**
     * 验证回调签名
     */
    public abstract Boolean verifyCallback(Map<String, Object> callbackData, PaymentChannelConfig config);
    
    /**
     * 获取支持的币种
     */
    public abstract List<String> getSupportedCurrencies();
    
    /**
     * 获取支持的支付方式
     */
    public abstract List<String> getSupportedMethods();
}
```

## 3. API接口设计规范

### 3.1 接口命名规范

#### 3.1.1 用户模块API
- `POST /api/v1/auth/login` - 用户登录
- `POST /api/v1/auth/register` - 用户注册
- `GET /api/v1/auth/oauth/{loginType}` - 第三方登录授权
- `POST /api/v1/auth/oauth/callback/{loginType}` - 第三方登录回调
- `POST /api/v1/auth/send-code` - 发送验证码
- `POST /api/v1/auth/verify-code` - 验证验证码
- `POST /api/v1/auth/refresh-token` - 刷新令牌
- `POST /api/v1/auth/logout` - 用户登出
- `GET /api/v1/user/info` - 获取用户信息
- `PUT /api/v1/user/profile` - 更新用户资料
- `POST /api/v1/user/deactivate` - 用户注销

#### 3.1.2 支付模块API
- `POST /api/v1/payment/create-order` - 创建支付订单
- `GET /api/v1/payment/order/{orderNo}` - 查询支付订单
- `POST /api/v1/payment/cancel/{orderNo}` - 取消支付订单
- `POST /api/v1/payment/refund` - 申请退款
- `GET /api/v1/payment/channels` - 获取支付渠道
- `POST /api/v1/payment/callback/{channelCode}` - 支付回调
- `POST /api/v1/payment/sync-status/{orderNo}` - 同步支付状态

### 3.2 响应格式规范

#### 3.2.1 统一响应格式
```java
@Data
@ApiModel("统一响应格式")
public class R<T> {
    @ApiModelProperty("响应码")
    private Integer code;
    
    @ApiModelProperty("响应消息")
    private String message;
    
    @ApiModelProperty("响应数据")
    private T data;
    
    @ApiModelProperty("时间戳")
    private Long timestamp;
    
    @ApiModelProperty("请求ID")
    private String requestId;
    
    public static <T> R<T> success(T data) {
        return new R<>(200, "success", data);
    }
    
    public static <T> R<T> fail(String message) {
        return new R<>(500, message, null);
    }
}
```

### 3.3 缓存管理接口设计

#### 3.3.1 缓存管理控制器 (CacheManagerController.java)
```java
@RestController
@RequestMapping("/api/v1/cache")
@Api(tags = "缓存管理接口")
@SaCheckRole("admin")
public class CacheManagerController {
    
    @DeleteMapping("/clear/{cacheName}")
    @ApiOperation("清空指定缓存")
    public R<Boolean> clearCache(@PathVariable String cacheName);
    
    @PostMapping("/refresh")
    @ApiOperation("刷新指定缓存")
    public R<Boolean> refreshCache(@RequestParam String cacheName, 
                                  @RequestParam String key);
    
    @DeleteMapping("/clear-batch")
    @ApiOperation("批量清空缓存")
    public R<Boolean> clearCaches(@RequestBody List<String> cacheNames);
    
    @GetMapping("/stats/{cacheName}")
    @ApiOperation("获取缓存统计")
    public R<CacheStatsVO> getCacheStats(@PathVariable String cacheName);
    
    @GetMapping("/list")
    @ApiOperation("获取所有缓存列表")
    public R<List<CacheInfoVO>> getCacheList();
}
```
